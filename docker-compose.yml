version: '3.8'

services:
  lp-editor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lp-editor-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env.docker
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - lp-editor-network

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: lp-editor-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - lp-editor
    restart: unless-stopped
    networks:
      - lp-editor-network
    profiles:
      - production

networks:
  lp-editor-network:
    driver: bridge

# Optional: Volumes for persistent data
volumes:
  lp-editor-data:
    driver: local
