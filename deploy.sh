#!/bin/bash

# LP Editor - One-Click Docker Deployment Script
# This script automates the deployment process for LP Editor

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main deployment function
main() {
    echo "=========================================="
    echo "  LP Editor - Docker Deployment Script"
    echo "=========================================="
    echo

    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi

    print_success "Docker and Docker Compose are installed."

    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    print_success "Docker daemon is running."

    # Setup environment file
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env.docker" ]; then
        if [ -f ".env.docker.example" ]; then
            cp .env.docker.example .env.docker
            print_warning "Created .env.docker from template."
            print_warning "Please edit .env.docker with your actual configuration values."
            echo
            echo "Required configurations:"
            echo "  - Tiptap Collab Cloud credentials"
            echo "  - AI service API keys (OpenAI, DeepSeek, etc.)"
            echo
            read -p "Press Enter after you've configured .env.docker, or Ctrl+C to exit..."
        else
            print_error ".env.docker.example not found. Cannot create environment file."
            exit 1
        fi
    else
        print_success "Environment file .env.docker already exists."
    fi

    # Build and start services
    print_status "Building Docker image..."
    docker-compose build --no-cache

    print_status "Starting LP Editor services..."
    docker-compose up -d

    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10

    # Check service health
    if docker-compose ps | grep -q "Up"; then
        print_success "LP Editor is now running!"
        echo
        echo "=========================================="
        echo "  Deployment Complete!"
        echo "=========================================="
        echo
        echo "🚀 Access your LP Editor at: http://localhost:3000"
        echo
        echo "📋 Useful commands:"
        echo "  View logs:     docker-compose logs -f"
        echo "  Stop services: docker-compose down"
        echo "  Restart:       docker-compose restart"
        echo "  Update:        ./deploy.sh"
        echo
        echo "🔧 Configuration file: .env.docker"
        echo
    else
        print_error "Failed to start services. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "LP Editor Docker Deployment Script"
        echo
        echo "Usage: $0 [OPTIONS]"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --stop         Stop all services"
        echo "  --restart      Restart all services"
        echo "  --logs         Show service logs"
        echo "  --status       Show service status"
        echo
        exit 0
        ;;
    --stop)
        print_status "Stopping LP Editor services..."
        docker-compose down
        print_success "Services stopped."
        exit 0
        ;;
    --restart)
        print_status "Restarting LP Editor services..."
        docker-compose restart
        print_success "Services restarted."
        exit 0
        ;;
    --logs)
        docker-compose logs -f
        exit 0
        ;;
    --status)
        docker-compose ps
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information."
        exit 1
        ;;
esac
