# LP Editor - Windows PowerShell Deployment Script
# This script automates the deployment process for LP Editor on Windows

param(
    [string]$Action = "deploy"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Start-Deployment {
    Write-Host "==========================================" -ForegroundColor $Blue
    Write-Host "  LP Editor - Docker Deployment Script" -ForegroundColor $Blue
    Write-Host "==========================================" -ForegroundColor $Blue
    Write-Host

    # Check prerequisites
    Write-Status "Checking prerequisites..."
    
    if (-not (Test-Command "docker")) {
        Write-Error "Docker is not installed. Please install Docker Desktop first."
        Write-Host "Visit: https://docs.docker.com/desktop/install/windows/"
        exit 1
    }

    if (-not (Test-Command "docker-compose")) {
        Write-Error "Docker Compose is not installed. Please install Docker Compose first."
        Write-Host "Visit: https://docs.docker.com/compose/install/"
        exit 1
    }

    Write-Success "Docker and Docker Compose are installed."

    # Check if Docker daemon is running
    try {
        docker info | Out-Null
        Write-Success "Docker daemon is running."
    }
    catch {
        Write-Error "Docker daemon is not running. Please start Docker Desktop first."
        exit 1
    }

    # Setup environment file
    Write-Status "Setting up environment configuration..."
    
    if (-not (Test-Path ".env.docker")) {
        if (Test-Path ".env.docker.example") {
            Copy-Item ".env.docker.example" ".env.docker"
            Write-Warning "Created .env.docker from template."
            Write-Warning "Please edit .env.docker with your actual configuration values."
            Write-Host
            Write-Host "Required configurations:"
            Write-Host "  - Tiptap Collab Cloud credentials"
            Write-Host "  - AI service API keys (OpenAI, DeepSeek, etc.)"
            Write-Host
            Read-Host "Press Enter after you've configured .env.docker, or Ctrl+C to exit"
        }
        else {
            Write-Error ".env.docker.example not found. Cannot create environment file."
            exit 1
        }
    }
    else {
        Write-Success "Environment file .env.docker already exists."
    }

    # Build and start services
    Write-Status "Building Docker image..."
    docker-compose build --no-cache

    Write-Status "Starting LP Editor services..."
    docker-compose up -d

    # Wait for services to be ready
    Write-Status "Waiting for services to start..."
    Start-Sleep -Seconds 10

    # Check service health
    $services = docker-compose ps --format json | ConvertFrom-Json
    $running = $services | Where-Object { $_.State -eq "running" }
    
    if ($running) {
        Write-Success "LP Editor is now running!"
        Write-Host
        Write-Host "==========================================" -ForegroundColor $Green
        Write-Host "  Deployment Complete!" -ForegroundColor $Green
        Write-Host "==========================================" -ForegroundColor $Green
        Write-Host
        Write-Host "🚀 Access your LP Editor at: http://localhost:3000" -ForegroundColor $Green
        Write-Host
        Write-Host "📋 Useful commands:"
        Write-Host "  View logs:     docker-compose logs -f"
        Write-Host "  Stop services: docker-compose down"
        Write-Host "  Restart:       .\deploy.ps1 -Action restart"
        Write-Host "  Status:        .\deploy.ps1 -Action status"
        Write-Host
        Write-Host "🔧 Configuration file: .env.docker"
        Write-Host
    }
    else {
        Write-Error "Failed to start services. Check logs with: docker-compose logs"
        exit 1
    }
}

function Stop-Services {
    Write-Status "Stopping LP Editor services..."
    docker-compose down
    Write-Success "Services stopped."
}

function Restart-Services {
    Write-Status "Restarting LP Editor services..."
    docker-compose restart
    Write-Success "Services restarted."
}

function Show-Logs {
    docker-compose logs -f
}

function Show-Status {
    docker-compose ps
}

function Show-Help {
    Write-Host "LP Editor Docker Deployment Script for Windows"
    Write-Host
    Write-Host "Usage: .\deploy.ps1 [-Action <action>]"
    Write-Host
    Write-Host "Actions:"
    Write-Host "  deploy     Deploy the application (default)"
    Write-Host "  stop       Stop all services"
    Write-Host "  restart    Restart all services"
    Write-Host "  logs       Show service logs"
    Write-Host "  status     Show service status"
    Write-Host "  help       Show this help message"
    Write-Host
}

# Main script logic
switch ($Action.ToLower()) {
    "deploy" { Start-Deployment }
    "stop" { Stop-Services }
    "restart" { Restart-Services }
    "logs" { Show-Logs }
    "status" { Show-Status }
    "help" { Show-Help }
    default {
        Write-Error "Unknown action: $Action"
        Show-Help
        exit 1
    }
}
