# LP Editor Docker Environment Configuration
# Copy this file to .env.docker and fill in your actual values

# ===========================================
# Tiptap Collab Cloud Configuration
# ===========================================
# Get these values from https://cloud.tiptap.dev/apps
NEXT_PUBLIC_TIPTAP_COLLAB_APP_ID=your_tiptap_app_id_here
TIPTAP_COLLAB_SECRET=your_tiptap_secret_here

# Document prefix for Tiptap Collab Cloud documents
NEXT_PUBLIC_COLLAB_DOC_PREFIX=doc_

# ===========================================
# AI Cloud Configuration
# ===========================================
# Tiptap AI Configuration (requires paid plan)
# Get these values from https://cloud.tiptap.dev/ai-settings
NEXT_PUBLIC_TIPTAP_AI_APP_ID=your_tiptap_ai_app_id_here
TIPTAP_AI_SECRET=your_tiptap_ai_secret_here

# ===========================================
# Custom AI Model Configuration
# ===========================================
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# DeepSeek Configuration (recommended for cost-effectiveness)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Zhipu AI Configuration
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions

# Anthropic Claude Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com

# ===========================================
# Application Configuration
# ===========================================
# Next.js Configuration
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
PORT=3000
HOSTNAME=0.0.0.0

# ===========================================
# Optional: Custom Domain Configuration
# ===========================================
# If using custom domain with nginx
# DOMAIN=your-domain.com
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===========================================
# Development/Debug Configuration
# ===========================================
# Set to true for development mode
# DEBUG=false
# LOG_LEVEL=info
