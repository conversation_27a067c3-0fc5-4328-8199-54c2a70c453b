# LP Editor Docker 部署指南

## 🚀 快速开始

### 1. 安装 Docker

**Windows:**
1. 下载 [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows/)
2. 运行安装程序并重启计算机
3. 启动 Docker Desktop
4. 验证安装：打开 PowerShell 运行 `docker --version`

**macOS:**
1. 下载 [Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
2. 拖拽到 Applications 文件夹
3. 启动 Docker Desktop
4. 验证安装：打开终端运行 `docker --version`

**Linux (Ubuntu/Debian):**
```bash
# 更新包索引
sudo apt-get update

# 安装必要的包
sudo apt-get install ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 设置仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker Engine
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 验证安装
docker --version
docker-compose --version
```

### 2. 配置项目

```bash
# 克隆项目
git clone <your-repo-url>
cd LP_Editor

# 复制环境变量模板
cp .env.docker.example .env.docker

# 编辑环境变量文件
# Windows: notepad .env.docker
# macOS/Linux: nano .env.docker
```

### 3. 配置 API 密钥

编辑 `.env.docker` 文件，填入以下配置：

#### Tiptap Cloud 配置
1. 访问 [Tiptap Cloud](https://cloud.tiptap.dev/register) 注册账号
2. 在 [Apps 页面](https://cloud.tiptap.dev/apps) 创建应用
3. 复制 App ID 和 Secret 到环境变量

#### AI 服务配置 (选择一个或多个)

**DeepSeek (推荐 - 性价比高):**
1. 访问 [DeepSeek](https://platform.deepseek.com/) 注册
2. 获取 API Key
3. 配置 `DEEPSEEK_API_KEY`

**OpenAI:**
1. 访问 [OpenAI Platform](https://platform.openai.com/) 
2. 获取 API Key
3. 配置 `OPENAI_API_KEY`

**智谱AI:**
1. 访问 [智谱AI](https://open.bigmodel.cn/) 注册
2. 获取 API Key  
3. 配置 `ZHIPU_API_KEY`

### 4. 启动应用

**Windows (PowerShell):**
```powershell
# 方式1: 使用部署脚本
.\deploy.ps1

# 方式2: 直接使用 docker-compose
docker-compose up -d --build
```

**macOS/Linux:**
```bash
# 方式1: 使用部署脚本
./deploy.sh

# 方式2: 直接使用 docker-compose
docker-compose up -d --build
```

### 5. 访问应用

- 🌐 应用地址: http://localhost:3000
- 📊 健康检查: http://localhost:3000/api/health (如果有)

## 🔧 管理命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f lp-editor
```

### 重启服务
```bash
docker-compose restart
```

### 停止服务
```bash
docker-compose down
```

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up -d --build
```

## 🐛 故障排除

### 常见问题

**1. 端口被占用**
```bash
# 检查端口占用
netstat -tulpn | grep 3000  # Linux/macOS
netstat -ano | findstr 3000  # Windows

# 修改端口 (编辑 docker-compose.yml)
ports:
  - "3001:3000"  # 改为 3001 端口
```

**2. 构建失败**
```bash
# 清理 Docker 缓存
docker system prune -a

# 重新构建
docker-compose build --no-cache
```

**3. 服务无法启动**
```bash
# 查看详细错误
docker-compose logs lp-editor

# 检查配置
docker-compose config
```

**4. AI 功能不工作**
- 检查 API 密钥是否正确配置
- 验证网络连接
- 查看应用日志中的具体错误信息

### 性能优化

**1. 增加内存限制**
```yaml
# 在 docker-compose.yml 中添加
services:
  lp-editor:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

**2. 启用生产模式**
```bash
# 使用 Nginx 反向代理
docker-compose --profile production up -d
```

## 📚 更多资源

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Tiptap 文档](https://tiptap.dev/docs)
- [Next.js 部署指南](https://nextjs.org/docs/deployment)
