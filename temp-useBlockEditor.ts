import { useEffect, useState, useRef } from 'react'
import { useEditor, useEditorState } from '@tiptap/react'
import { AnyExtension, Editor, EditorOptions, Extension } from '@tiptap/core'
import Collaboration from '@tiptap/extension-collaboration'
import CollaborationCursor from '@tiptap/extension-collaboration-cursor'
import { TiptapCollabProvider, WebSocketStatus } from '@hocuspocus/provider'
import type { Doc as YDoc } from 'yjs'
import { Plugin, PluginKey, Transaction, EditorState, TextSelection } from 'prosemirror-state'
import { EditorView } from 'prosemirror-view'
import { Decoration, DecorationSet } from 'prosemirror-view'

import { ExtensionKit } from '@/extensions/extension-kit'
import { userColors, userNames } from '../lib/constants'
import { randomElement } from '../lib/utils'
import type { EditorUser } from '../components/BlockEditor/types'
import { initialContent } from '@/lib/data/initialContent'

declare global {
  interface Window {
    editor: Editor | null
  }
}

// --- Custom Extension Logic ---

// Plugin Keys
const autocompleteKeydownPluginKey = new PluginKey('autocompleteKeydownHandler');
const suggestionDecorationPluginKey = new PluginKey('suggestionDecoration');

// Decoration Plugin State - Simplified: Only store what needs to be displayed
interface SuggestionDecorationState {
  fullSuggestion: string | null;     // Store the full suggestion
  position: number | null;
  displayLength: number;          // How many characters to display currently
}

// Interface for options passed to the extension
interface AutocompleteHandlerOptions {
  isAutocompletionEnabled: boolean;
}

// --- Plugin View for managing the typing interval ---
class SuggestionView {
  private view: EditorView;
  private intervalId: NodeJS.Timeout | null = null;
  private typingSpeed = 50; // ms per character

  constructor(view: EditorView) {
    this.view = view;
    this.update(view, null); // Initial check
  }

  update(view: EditorView, prevState: EditorState | null) {
    this.view = view; // Keep view reference updated
    const newState = suggestionDecorationPluginKey.getState(view.state);
    const oldPluginState = prevState ? suggestionDecorationPluginKey.getState(prevState) : null;

    // console.log("SuggestionView Update:", newState, oldPluginState); // Debug log

    // Did we just start showing a suggestion?
    if (newState?.fullSuggestion && !oldPluginState?.fullSuggestion) {
      this.clear(); // Clear any old interval
      console.log("SuggestionView starting typing interval...");
      this.intervalId = setInterval(() => {
        // Get latest state *inside* interval
        const currentState = suggestionDecorationPluginKey.getState(this.view.state);
        if (!currentState?.fullSuggestion || currentState.displayLength >= currentState.fullSuggestion.length) {
          this.clear(); // Stop if suggestion cleared or fully typed
          return;
        }

        // Dispatch transaction to increase displayLength
        this.view.dispatch(
          this.view.state.tr.setMeta(suggestionDecorationPluginKey, {
            action: 'incrementDisplay'
          })
        );
      }, this.typingSpeed);
    }
    // Did the suggestion just get cleared?
    else if (!newState?.fullSuggestion && oldPluginState?.fullSuggestion) {
      console.log("SuggestionView clearing interval because suggestion ended.");
      this.clear();
    }
  }

  destroy() {
    console.log("SuggestionView destroy: Clearing interval.");
    this.clear();
  }

  clear() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}

const AutocompleteHandler = Extension.create<AutocompleteHandlerOptions>({
  name: 'autocompleteHandler',

  addOptions() {
    return { isAutocompletionEnabled: false };
  },

  addProseMirrorPlugins() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const extensionThis = this;

    // --- Keydown Plugin (Handles triggers and acceptance) ---
    const keydownPlugin = new Plugin({
      key: autocompleteKeydownPluginKey,
      props: {
        handleKeyDown: (view: EditorView, event: KeyboardEvent): boolean => {
          const { isAutocompletionEnabled } = extensionThis.options;
          const decorationState = suggestionDecorationPluginKey.getState(view.state);

          console.log(`Keydown: key=${event.key}, shift=${event.shiftKey}, enabled=${isAutocompletionEnabled}, showingFull=${decorationState?.fullSuggestion}`);

          const isShiftTab = event.shiftKey && event.key === 'Tab';

          // Handle Shift+Tab
          if (isAutocompletionEnabled && isShiftTab) {
            event.preventDefault();

            if (decorationState?.fullSuggestion) {
              // --- Second Shift+Tab: Accept suggestion IMMEDIATELY ---
              const fullSuggestionText = decorationState.fullSuggestion;
              console.log('Accepting suggestion immediately:', fullSuggestionText);
              const insertPos = decorationState.position !== null ? decorationState.position : view.state.selection.head;
              let tr = view.state.tr.insertText(fullSuggestionText, insertPos);
              const newPos = insertPos + fullSuggestionText.length;
              tr = tr.setSelection(TextSelection.create(tr.doc, newPos));
              tr = tr.setMeta(suggestionDecorationPluginKey, { action: 'clear' }); // Clear decoration state
              view.dispatch(tr);
            } else {
              // --- First Shift+Tab: Trigger suggestion ---
              console.log('Triggering AI suggestion (mock)...');
              setTimeout(() => {
                if (view.hasFocus()) {
                  const mockSuggestion = ' (这是模拟的 AI 建议这是模拟的 AI 建议)这是模拟的 AI 建议)这是模拟的 AI 建议)这是模拟的 AI 建议)这是模拟的 AI 建议)这是模拟的 AI 建议)) ';
                  console.log('Mock suggestion ready:', mockSuggestion);
                  // Dispatch meta to START showing suggestion (typing handled by SuggestionView)
                  view.dispatch(
                    view.state.tr.setMeta(suggestionDecorationPluginKey, {
                      action: 'show',
                      fullSuggestion: mockSuggestion,
                      position: view.state.selection.head
                    })
                  );
                } else {
                  console.log('Editor lost focus, aborting mock suggestion.');
                   view.dispatch(
                     view.state.tr.setMeta(suggestionDecorationPluginKey, { action: 'clear' })
                   );
                }
              }, 500);
            }
            return true; // Event handled
          }

          // Clear suggestion on other key presses
          if (decorationState?.fullSuggestion && !isShiftTab && event.key !== 'Shift') {
              console.log(`Clearing suggestion due to key press: ${event.key}`);
              view.dispatch(
                view.state.tr.setMeta(suggestionDecorationPluginKey, { action: 'clear' })
              );
              return false; // Allow original key press
          }
          return false; // Let others handle
        },
      },
    });

    // --- Decoration Plugin (Manages state and renders decoration) ---
    const decorationPlugin = new Plugin<SuggestionDecorationState>({ // Specify plugin state type
      key: suggestionDecorationPluginKey,
      state: {
        init(): SuggestionDecorationState {
          // Initial state only needs displayLength reset
          return { fullSuggestion: null, position: null, displayLength: 0 };
        },
        apply(tr: Transaction, oldState: SuggestionDecorationState): SuggestionDecorationState {
          const meta = tr.getMeta(suggestionDecorationPluginKey);

          if (meta) {
            console.log("Decoration Plugin Apply Meta:", meta);
            if (meta.action === 'show' && meta.fullSuggestion) {
              // Start showing: reset displayLength
              return { fullSuggestion: meta.fullSuggestion, position: meta.position, displayLength: 0 };
            } else if (meta.action === 'incrementDisplay' && oldState.fullSuggestion) {
              // Increment displayLength, capped at full length
              const newLength = Math.min(oldState.displayLength + 1, oldState.fullSuggestion.length);
              return { ...oldState, displayLength: newLength };
            } else if (meta.action === 'clear') {
              // Clear everything
              return { fullSuggestion: null, position: null, displayLength: 0 };
            }
          }

          // If doc/selection changes while showing, clear it
          if ((tr.docChanged || tr.selectionSet) && oldState.fullSuggestion) {
            console.log("Clearing decoration due to doc/selection change");
            return { fullSuggestion: null, position: null, displayLength: 0 };
          }

          return oldState; // No relevant change
        },
      },
      props: {
        decorations(state: EditorState): DecorationSet | null {
          const pluginState = this.getState(state);

          // Render based on displayLength
          if (pluginState?.fullSuggestion && pluginState.position !== null && pluginState.displayLength > 0 && state.selection.empty && state.selection.head === pluginState.position) {
            const displayedText = pluginState.fullSuggestion.substring(0, pluginState.displayLength);
            // console.log("Creating decoration:", displayedText); // Log sparingly if needed
            const widget = document.createElement('span');
            widget.textContent = displayedText;
            widget.className = 'suggestion-ghost-text text-gray-400 pointer-events-none relative';

            return DecorationSet.create(state.doc, [
              Decoration.widget(pluginState.position, widget, { side: 1, marks: [] })
            ]);
          }
          return null;
        },
      },
      // Use the Plugin View to manage the interval
      view(editorView) { return new SuggestionView(editorView); }
    });

    return [keydownPlugin, decorationPlugin];
  },
});

// --- Hook Definition ---

export const useBlockEditor = ({
  ydoc,
  provider,
  userId,
  userName = 'Maxi',
  isAutocompletionEnabled,
  ...editorOptions
}: {
  ydoc: YDoc | null
  provider?: TiptapCollabProvider | null | undefined
  userId?: string
  userName?: string
  isAutocompletionEnabled: boolean;
} & Partial<Omit<EditorOptions, 'extensions'>>) => {
  const [collabState, setCollabState] = useState<WebSocketStatus>(
    provider ? WebSocketStatus.Connecting : WebSocketStatus.Disconnected,
  )

  const editor = useEditor(
    {
      ...editorOptions,
      immediatelyRender: true,
      shouldRerenderOnTransaction: false,
      autofocus: true,
      onCreate: ctx => {
        if (provider && !provider.isSynced) {
          provider.on('synced', () => {
            setTimeout(() => {
              if (ctx.editor.isEmpty) {
                ctx.editor.commands.setContent(initialContent)
              }
            }, 0)
          })
        } else if (ctx.editor.isEmpty) {
          ctx.editor.commands.setContent(initialContent)
          ctx.editor.commands.focus('start', { scrollIntoView: true })
        }
        if (ctx.editor.view) {
          ctx.editor.view.dispatch(
              ctx.editor.state.tr.setMeta(suggestionDecorationPluginKey, { action: 'clear' })
          );
        }
      },
      extensions: [
        AutocompleteHandler.configure({
          isAutocompletionEnabled,
        }),
        ...ExtensionKit({
          provider,
        }),
        provider && ydoc
          ? Collaboration.configure({
              document: ydoc,
            })
          : undefined,
        provider
          ? CollaborationCursor.configure({
              provider,
              user: {
                name: randomElement(userNames),
                color: randomElement(userColors),
              },
            })
          : undefined
      ].filter((e): e is AnyExtension => e !== undefined),
      editorProps: {
        attributes: {
          autocomplete: 'off',
          autocorrect: 'off',
          autocapitalize: 'off',
          class: 'min-h-full',
        },
      },
    },
    [ydoc, provider, isAutocompletionEnabled],
  )
  const users = useEditorState({
    editor,
    selector: (ctx): (EditorUser & { initials: string })[] => {
      if (!ctx.editor?.storage.collaborationCursor?.users) {
        return []
      }

      return ctx.editor.storage.collaborationCursor.users.map((user: EditorUser) => {
        const names = user.name?.split(' ')
        const firstName = names?.[0]
        const lastName = names?.[names.length - 1]
        const initials = `${firstName?.[0] || '?'}${lastName?.[0] || '?'}`

        return { ...user, initials: initials.length ? initials : '?' }
      })
    },
  })

  useEffect(() => {
    provider?.on('status', (event: { status: WebSocketStatus }) => {
      setCollabState(event.status)
    })
  }, [provider])

  window.editor = editor

   useEffect(() => {
    return () => {
       console.log("BlockEditor effect cleanup for editor dependency");
    };
  }, [editor]);

  return { editor, users, collabState }
}
