# Tiptap Pro License

Tiptap GmbH grants you an ongoing, non-exclusive license to use the Tiptap React templates, as long as you are an active subscriber of Tiptap Pro.

If you have purchased the Free Plan, the license grants permission for **one individual** (the Licensee) to access and use the Tiptap React templates.

If you have purchased the Starter Plan, the license grants permission for **up to 8 Employees of the Licensee** to access and use the Tiptap React templates.

If you have purchased the Business Plan, the license grants permission for **unlimited Employees of the Licensee** to access and use the Tiptap React templates.

You **can**:

- Use the Tiptap React templates to create unlimited End Products.
- Use the Tiptap React templates to create unlimited End Products for unlimited Clients.
- Use the Tiptap React templates to create End Products where the End Product is sold to End Users.
- Use the Tiptap React templates to create End Products that are open source and freely available to End Users.

You **cannot**:

- Use the Tiptap React templates to create End Products that are developed to allow an End User to build their own End Products using the Tiptap React templates or derivatives of the Tiptap React templates.
- Modify the Tiptap React templates to create derivative Tiptap React templates.
- Re-distribute the Tiptap React templates or derivatives of the Tiptap React templates separately from an End Product.
- Share your account or access to the Tiptap React templates with any other individuals, if you have purchased a Free Plan.
- Use the Tiptap React templates to create End Products that are the property of any individual or entity other than the Licensee or Clients of the Licensee.
- Use the Tiptap React templates to produce anything that, in the sole and absolute discretion of Tiptap GmbH, may be considered competitive or collusive with Tiptap GmbH's business.

## Example usage

Examples of usage **allowed** by the license:

- Creating a personal website or web application by yourself.
- Creating a website or web application for a client that will be owned by that client.
- Creating a commercial SaaS application (like an invoicing app for example) where end users have to pay a fee to use the application.
- Creating a commercial self-hosted web application that is sold to end users for a one-time fee.
- Creating a web application whose main purpose is clearly not to share the Tiptap React templates (such as a conference organization application that uses the Tiptap React templates for its editor) that is free and open source and whose source code is publicly available.

Examples of use **not permitted** by the license:

- Creating a repository of your favorite Tiptap React templates (or derivatives based on Tiptap React templates) and publishing it publicly.
- Rebuilding Tiptap React templates in other programming languages or program libraries and making it available either for sale or for free.
- Creating an "editor builder" project where end users can build their own editors using Tiptap React templates included with or derived from Tiptap React templates.
- Creating any End Product that is not the sole property of either you, your company or a client of your company. For example your employees/contractors can't use your company’s Tiptap Pro license to build their own websites or side projects.

## Enforcement

If you are found to be in violation of the license, access to your Tiptap account will be terminated, and a refund may be issued at our discretion. When license violation is blatant and malicious (such as intentionally redistributing the Tiptap React templates through private warez channels), no refund will be issued.

The copyright of the Tiptap React templates is owned by Tiptap GmbH. You are granted only the permissions described in this license; all other rights are reserved. Tiptap GmbH reserves the right to take legal action against any unauthorized use of the Tiptap React templates outside of this license. In particular, Tiptap GmbH reserves the right to terminate your license without notice if you fail to comply with any term or condition of this License Agreement. Upon termination of this License Agreement, you must destroy the original and all copies of the Tiptap React templates and related documentation.

## Liability

Tiptap GmbH’s liability to you for costs, damages, or other losses arising from your use of the Tiptap React templates — including third-party claims against you — is limited to the amount paid by the Licensee in the 12 months prior to the occurrence of the damage event. Tiptap GmbH may not be held liable for any consequential damages, incidental or special damages, including any damages for loss of profits or savings related to your use of the Tiptap React templates. Tiptap GmbH shall have unlimited liability for damage arising from gross negligence or wilful intent and in the event of injury to life, body or health.

The liability of Tiptap GmbH irrespective of fault in accordance with Section 536a(1), 1st alternative German Civil Code for defects that already existed at the time of granting this license is excluded. 

## Final Provisions

This agreement is governed by the laws of the Federal Republic of Germany, excluding the conflict of laws provisions and excluding the United Nations Convention on Contracts for the International Sale of Goods (CISG). The place of jurisdiction for all disputes arising from this contractual relationship, as far as legally permitted, is Berlin.

## Questions?

Unsure if your use case is covered by our license? Email <NAME_EMAIL>!
