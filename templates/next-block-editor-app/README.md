# Next BlockEditor 应用

## 使用指南

BlockEditor模板是一个功能完善的Next.js应用程序，类似于Notion或Dropbox Paper，适合作为您项目的基础或作为自定义编辑器的基础。

模板的主要特点包括：

- 使用TypeScript的Next.js设置
- 基本的Tailwind样式设置
- 预配置的Tiptap Cloud链接，用于协作和数据持久化
- 具有基本节点和标记集的块编辑器，还包括更高级的功能，如：
  - 通过拖动手柄实现拖放
  - 用于文本编辑和格式设置的完善菜单栏
  - 链接编辑
  - 可连接到后端的模拟图片上传
  - 格式刷工具，用于复制和应用文本格式
  - 打字效果扩展，为选中文本添加补充内容时产生打字动画效果

您可以从这个编辑器开始一个全新的项目，或者将编辑器或您需要的部分复制到您自己的项目中。
请查看[Tiptap文档](https://tiptap.dev)以获取有关如何使用Tiptap的更多信息。

## 格式刷工具

编辑器包含一个类似于Microsoft Word的格式刷工具，允许您从文本的一个部分复制格式并应用到另一个部分。

### 格式刷使用方法

1. **选择**带有您想要复制的格式的文本（包括粗体/斜体等文本标记和标题等节点类型）
2. **点击**工具栏中的格式刷按钮（刷子图标）- 激活时按钮会高亮显示
3. **选择**您想要应用格式的文本
4. 格式将自动应用，格式刷工具会自动停用

### 支持的格式

格式刷支持复制和应用以下格式：

- **文本标记**：
  - 粗体、斜体、下划线、删除线
  - 文本颜色和高亮
  - 字体大小和字体
  - 上标和下标

- **节点类型**：
  - 标题（保留级别）
  - 段落
  - 引用块
  - 代码块
  - 列表（无序列表、有序列表和任务列表）

- **文本属性**：
  - 文本对齐（左对齐、居中、右对齐、两端对齐）

### 实现细节

格式刷作为一个Tiptap扩展实现，它可以：
- 捕获所选文本的格式
- 在激活状态下提供视觉反馈
- 使用Tiptap的命令API安全地将格式应用到目标文本
- 单次使用后自动停用

**重要实现细节**：
- 格式刷只收集选定范围内**第一个**非文本节点的类型和属性（如标题级别）
- 同时收集选定范围内**所有**文本标记（如粗体、斜体、颜色等）
- 当一段文本包含多个属性和标记时（例如同时具有粗体和颜色），所有这些属性都会被一起应用
- 应用格式时，先移除目标文本的所有现有标记，然后应用所有复制的标记和节点类型

这种实现方式确保了格式刷功能与Microsoft Word中的行为相似，可以一次性应用多种格式属性。

> [!重要]
> 请注意，这些模板仅供免费评估使用。如果您希望使用Tiptap的付费功能，您需要遵守[Tiptap Pro许可证](https://tiptap.dev/pro-license)。总结一下，您可以在开发中或出于个人原因免费使用Tiptap Pro扩展，但在生产环境或商业用途中使用它们需要购买许可证。
> 本代码库中的所有代码均受[Tiptap Pro许可证](https://tiptap.dev/pro-license)许可，未经有效许可证不得分发或在生产环境中使用。

## 文件夹结构

模板结构为一个Next.js应用，带有一些额外的文件夹和文件：

- **components** 包含应用中使用的所有React组件
  - **BlockEditor** 包装BlockEditor设置组件
  - **menus** 编辑器中使用的一系列菜单（例如链接、文本和拖动手柄菜单）
  - **panels** 菜单栏中使用的弹出菜单，例如颜色选择器或链接编辑器
  - **Sidebar** 侧边栏组件
  - **TableOfContents** 用于导航的目录组件
  - **ui** 整个应用中使用的通用可重用UI组件
- **context** 放置应用中使用的React上下文的地方
- **extensions** 包含应用中使用的所有Tiptap扩展，包括它们的NodeViews和逻辑部分
  - **FormatPainter.ts** 实现复制和应用文本格式的格式刷扩展
- **hooks** 包含用于应用和编辑器设置的通用钩子
- **lib** 包含辅助函数
- **styles** 包含具有Tailwind的全局CSS样式，用于设置富文本样式

## 字体选择功能

编辑器支持多种字体选择，可以为文本应用不同的字体样式。

#### 技术实现

- 使用 TipTap 的 FontFamily 扩展实现字体选择功能
- 字体选择通过顶部导航栏和气泡菜单中的字体下拉选择器进行操作
- 采用与 Microsoft Word 相似的字体分组显示方式：主题字体、中文字体和西文字体
- 支持常用中英文字体，包括微软雅黑、宋体、Arial、Times New Roman 等
- 字体应用为内联样式，使用 `<span style="font-family: ...">` 标签

#### 使用方法

1. 选择要应用字体的文本
2. 通过以下任一方式选择字体：
   - 在顶部工具栏的"字体"下拉菜单中选择所需字体
   - 或在选中文本上出现的气泡菜单中，点击字体下拉框选择
3. 字体样式将立即应用到所选文本上
4. 当光标位于已设置字体的文本上时，下拉菜单会自动显示当前文本的字体

#### 注意事项

- 字体选择界面模仿 Microsoft Word 的分组显示方式，提供更直观的选择体验
- 每个字体选项使用其对应的字体样式显示，方便预览效果
- 可用的字体根据用户设备上已安装的字体而定
- 字体设置依赖于 TextStyle 扩展，它将创建一个 `<span>` 标签并应用内联样式
- 当光标位于不同字体的文本交界处时，编辑器可能会显示默认字体（微软雅黑）
- 使用"清除格式"按钮可以移除已应用的字体样式
- 格式刷功能支持复制字体样式

### 字体大小调整功能

编辑器支持增大和减小文本字体大小，可以轻松调整文本的显示尺寸。

#### 技术实现

- 使用 TipTap 的 FontSize 扩展实现字体大小调整功能 
- 在顶部导航栏和气泡菜单中添加了字体大小控制功能：
  - 字体大小下拉选择器（包含 8pt 到 72pt 的常用字号）
  - 增大字号按钮（向上箭头）
  - 减小字号按钮（向下箭头）
- 支持多个预设字体大小：8px、9px、10px、12px、14px、16px、18px、20px、22px、24px、26px、28px、36px、48px、72px
- 字体大小应用为内联样式，使用 `<span style="font-size: ...">` 标签

#### 使用方法

1. 选择要调整字体大小的文本
2. 通过以下任一方式调整字体大小：
   - 直接从下拉菜单中选择特定的字体大小
   - 点击增大字号按钮（↑）逐级增大字体
   - 点击减小字号按钮（↓）逐级减小字体
3. 无论在顶部菜单栏还是气泡菜单中，都可以使用相同的方式调整
4. 当前选中文本的字体大小值会显示在下拉菜单的按钮上

#### 注意事项

- 字体大小调整功能遵循预设的阶梯式变化，增大/减小按钮会在预设值之间跳转
- 当当前字体大小不在预设值中时，增大按钮会选择下一个更大的预设值
- 当达到最大预设值后，继续增大会按比例（增加20%）调整字体大小
- 当达到最小预设值后，继续减小会按比例（减少20%）调整字体大小，但不会小于8px
- 顶部菜单栏和气泡菜单中的字体大小控制保持一致的用户体验和操作逻辑
- 字体大小设置也依赖于 TextStyle 扩展，通常与字体、颜色等样式组合使用
- 使用"清除格式"按钮可以移除已应用的字体大小样式
- 格式刷功能支持复制字体大小样式

#### 开发说明

FontSize 扩展是从 `@tiptap/extension-font-size` 导入的，并需要 TextStyle 扩展作为先决条件。关键命令包括：

- `setFontSize(size)`: 设置选定文本的字体大小
- `unsetFontSize()`: 移除选定文本的字体大小样式
- `editor.getAttributes('textStyle').fontSize`: 获取当前选定文本的字体大小

### 字体颜色功能

编辑器支持更改文本颜色，让您可以为文本应用各种颜色样式，增强文档的视觉表现力。

#### 技术实现

- 使用 Tiptap 的 Color 扩展实现字体颜色功能
- 在格式工具栏中添加了颜色选择按钮，使用调色板图标表示
- 颜色选择器包含两组预设颜色：
  - 默认颜色：包括黑、红、黄、绿、青、蓝、紫、银、白等基本颜色
  - 标准色：包括深红、亮红、金黄、鲜黄、橄榄绿、深绿、淡蓝、蓝色、深蓝、紫色等Office风格标准色
- 颜色应用为内联样式，使用 `<span style="color: ...">` 标签
- 支持清除已应用的颜色

#### 使用方法

1. 选择要应用颜色的文本
2. 点击格式工具栏中的颜色按钮（调色板图标）
3. 在弹出的颜色面板中选择所需颜色
4. 颜色将立即应用到所选文本上
5. 要移除已应用的颜色，可以点击颜色面板中的清除按钮

#### 注意事项

- 颜色选择器界面采用Office Word风格设计，提供了两组常用颜色
- 应用颜色后，颜色按钮会显示为激活状态，指示当前文本有颜色属性
- 当光标位于已设置颜色的文本上时，颜色按钮会保持激活状态
- 颜色设置依赖于TextStyle扩展和Color扩展的组合
- 使用"清除格式"按钮也可以移除已应用的颜色样式
- 格式刷功能支持复制和应用文本颜色
- 可以同时应用字体颜色和其他文本格式（如粗体、斜体等）

#### 开发说明

Color扩展是从`@tiptap/extension-color`导入的，它扩展了TextStyle扩展。关键命令包括：

- `setColor(color)`: 设置选定文本的颜色
- `unsetColor()`: 移除选定文本的颜色样式
- `editor.getAttributes('textStyle').color`: 获取当前选定文本的颜色值

### AI工具功能

编辑器集成了AI功能，可以帮助用户改写、扩展或总结选中的文本内容，提高内容创作效率。

#### 技术实现

- 使用自定义Tiptap扩展`AITools`实现AI辅助功能
- 对接项目中的`XAgentService`大模型服务
- 在气泡菜单中添加AI工具按钮，包含三个主要功能选项
- 使用`TypingEffect`扩展实现AI返回内容的打字动画效果
- 支持不同的AI操作类型，每种操作使用特定的提示词模板

#### 使用方法

1. 选择要处理的文本（可选，如果不选中文本将直接生成内容）
2. 通过以下任一方式调用AI工具：
   - **气泡菜单**：在气泡菜单中点击"AI工具"按钮，从下拉菜单中选择功能
   - **斜杠命令**：输入"/"，然后选择"AI"分类下的相应工具：
     - `/改写文本` - 保持原意，使用不同表达方式重写文本
     - `/扩展文本` - 基于选中文本添加更多细节和内容
     - `/总结文本` - 提取文本的关键要点并简洁表达
3. 选择功能后，AI开始处理文本，处理过程中按钮显示加载状态
4. 处理完成后，结果会以打字效果呈现：
   - 如果选中了文本：
     - 改写功能：直接替换原选中文本
     - 扩展和总结功能：在原文本后添加生成内容
   - 如果未选中文本：直接在当前光标位置插入AI生成的内容

#### 无选中文本时的处理

当用户没有选中任何文本时，AI工具不再显示默认测试文本，而是：
- 在后台使用预设的默认文本作为输入
- 直接将AI处理结果以打字效果呈现在光标位置
- 用户只能看到生成的结果，不会看到默认测试文本

这种方式让用户可以快速获取AI生成内容，而不必先选择或输入文本，提高了工具的便捷性和效率。

#### 注意事项

- 使用AI功能前，需确保已正确配置AI服务的API密钥（在系统设置中）
- AI工具需要选中文本才能使用，未选中文本时按钮为禁用状态
- 处理过程可能需要几秒钟时间，视文本长度和AI服务响应速度而定
- 生成的内容可能因不同的AI模型而有所差异
- 在处理过程中，建议不要修改文档内容，以免影响结果插入位置
- AI生成的内容可以进一步编辑或格式化，与普通文本无异
- 如遇到错误，请检查网络连接和API密钥设置

#### 开发说明

AITools扩展关键实现包括：

- 创建`AITools`扩展并注册在editor中
- 使用`XAgentService`发送文本到AI服务处理
- 根据不同工具类型（改写/扩展/总结）使用不同提示词模板
- 支持流式响应，但只在响应完成后一次性应用结果
- 使用`applyTypingEffect`函数实现逐字打字效果
- 对不同操作类型采用不同的文本处理策略（替换或添加）

### 字体背景色功能

编辑器支持为文本添加背景色高亮，使文本在文档中更加突出显示，便于标记重要内容。

#### 技术实现

- 使用 Tiptap 的 Highlight 扩展实现字体背景色功能
- 在格式工具栏中添加了背景色选择按钮，使用荧光笔图标表示
- 背景色选择器包含两组预设颜色：
  - 默认背景色：包括黄色、青色、绿色、紫色、红色、蓝色等明亮色彩
  - 浅色系：包括浅橙、浅绿、浅蓝、浅紫、浅黄等柔和色调，适合大面积文本高亮
- 背景色应用为标记样式，使用 `<mark style="background-color: ...">` 标签
- 支持清除已应用的背景色

#### 使用方法

1. 选择要应用背景色的文本
2. 点击格式工具栏中的背景色按钮（荧光笔图标）
3. 在弹出的颜色面板中选择所需背景色
4. 背景色将立即应用到所选文本上
5. 要移除已应用的背景色，可以点击背景色面板中的清除按钮

#### 注意事项

- 背景色选择器界面采用Office Word风格设计，提供了两组常用背景色
- 应用背景色后，背景色按钮会显示为激活状态，指示当前文本有背景色属性
- 当光标位于已设置背景色的文本上时，背景色按钮会保持激活状态
- 背景色设置依赖于Highlight扩展，支持多种颜色
- 使用"清除格式"按钮也可以移除已应用的背景色样式
- 格式刷功能支持复制和应用文本背景色
- 可以同时应用字体背景色和其他文本格式（如粗体、斜体、字体颜色等）
- 建议选择与文本颜色对比度高的背景色，以确保文本清晰可读

#### 开发说明

Highlight扩展是从`@tiptap/extension-highlight`导入的，已配置为支持多种颜色。关键命令包括：

- `setHighlight({ color })`: 设置选定文本的背景色
- `unsetHighlight()`: 移除选定文本的背景色样式
- `editor.getAttributes('highlight').color`: 获取当前选定文本的背景色值

### 行缩进功能

编辑器支持增加和减少段落或列表项的缩进，让您可以灵活调整文档结构和层次。

#### 技术实现

- 使用自定义的Indent扩展实现缩进控制
- 该扩展为paragraph、heading和listItem节点类型添加了indent属性
- 在格式工具栏中添加了两个缩进控制按钮：
  - 增加缩进按钮：使用向右缩进图标
  - 减少缩进按钮：使用向左缩进图标
- 这些按钮在文本对齐按钮组和列表按钮组之间
- 按钮图标使用蓝色箭头突出显示操作方向
- 支持Tab和Shift+Tab键盘快捷键进行缩进操作

#### 使用方法

1. 将光标放置在要调整缩进的段落或列表项中
2. 点击格式工具栏中的增加缩进按钮（向右箭头图标）增加缩进
3. 点击减少缩进按钮（向左箭头图标）减少缩进
4. 对于列表项，增加缩进会创建嵌套列表，减少缩进则会降低嵌套层级
5. 对于普通段落，缩进会以20像素为单位增加或减少左边距，最多支持5级缩进（100像素）
6. 也可以通过键盘快捷键Tab和Shift+Tab来增加和减少缩进

#### 注意事项

- 缩进功能统一使用了自定义的Indent扩展，可以处理各种内容类型：
  - 对于列表项，既可以使用Indent扩展，也可以使用Tiptap内置的列表缩进功能
  - 对于普通段落和标题，使用Indent扩展添加data-indent属性控制缩进
- 增加列表项缩进的替代方式是在列表项开头处按Tab键
- 减少列表项缩进的替代方式是在列表项开头处按Shift+Tab键
- 当列表项已经是最高级别时，减少缩进会将其转换为普通段落
- 段落的缩进有5个级别，每个级别增加20像素的左边距
- 当段落没有缩进时，减少缩进按钮操作不会有视觉变化
- 当段落已经达到最大缩进级别(5)时，增加缩进按钮操作不会有视觉变化
- 缩进调整会保留文本的其他格式设置
- 缩进操作不会改变文本的基本格式（如粗体、斜体等），仅调整位置和层级

#### 开发说明

缩进功能使用了自定义扩展实现：

- 创建了一个名为Indent的扩展，基于[Tiptap社区解决方案](https://stackoverflow.com/questions/75902464/indent-action-in-tiptap-2)
- 扩展为段落、标题和列表项添加了全局indent属性
- 添加了两个命令：
  - `indent()`: 增加缩进级别
  - `outdent()`: 减少缩进级别
- 添加了键盘快捷键支持：
  - Tab: 增加缩进
  - Shift+Tab: 减少缩进
- CSS使用data-indent属性选择器设置不同级别的缩进样式
- 最小缩进级别为0，最大缩进级别为5，与CSS样式对应

按钮处理逻辑会根据内容类型调用适当的方法：对于列表项可以使用专有的列表缩进命令，对于其他内容则使用Indent扩展的命令。

## 检查列表功能

编辑器支持创建交互式检查列表，类似于 Microsoft Word 中的待办事项列表。

### 技术实现

- 使用 TipTap 的 TaskList 和 TaskItem 扩展实现检查列表功能
- 检查列表可以通过顶部导航栏中的"检查列表"按钮创建，位于列表按钮组中
- 支持通过点击复选框来切换任务项的完成状态
- 检查列表项支持嵌套，允许创建多级任务结构
- 样式设计遵循 Office Word 风格，保持整体界面的一致性

### 使用方法

1. 在编辑器中选定要转换为检查列表的文本
2. 点击顶部工具栏中的"检查列表"按钮（显示为复选框列表图标）
3. 文本将转换为带有复选框的检查列表
4. 点击复选框可以切换任务完成状态
5. 完成的任务项会显示删除线样式
6. 通过斜杠命令（/task 或 /todo）也可以插入检查列表

### 注意事项

- 检查列表的复选框点击状态会自动保存
- 可以混合使用普通列表和检查列表
- 支持使用Tab键进行列表嵌套
- 编辑器样式已针对检查列表进行了优化，提供了清晰的视觉反馈
- 检查列表可以和格式刷功能结合使用
- 在导出文档时，检查列表的状态会被保留

### 开发说明

TaskList 和 TaskItem 扩展是从 `@tiptap/extension-task-list` 和 `@tiptap/extension-task-item` 导入的。关键命令包括：

- `toggleTaskList()`: 在普通段落和任务列表之间切换
- `editor.isActive('taskList')`: 检查当前选区是否在任务列表中
- TaskItem 已配置为支持嵌套（`nested: true`）
- 样式定义在 `styles/partials/lists.css` 文件中，使用 Tailwind 类

## 功能列表

### 显示/隐藏换行符

编辑器支持显示或隐藏文档中的换行符和段落标记，便于精确控制文档格式。

- 点击工具栏中的换行符图标可切换显示/隐藏状态
- 显示状态下，段落末尾会显示"¶"符号，硬换行处显示"↵"符号
- 使用Office Word风格的换行符，便于编辑时识别文档结构
- 换行符采用蓝色显示，以便于在文档中分辨

#### 使用场景

- 需要精确控制文档格式和排版时
- 排查文档中的格式问题
- 编辑复杂结构文档时帮助理解文档结构

#### 注意事项

- 换行符仅用于编辑参考，不会影响文档的实际内容和导出结果
- 此功能通过Tiptap编辑器的装饰器（Decoration）实现，不修改文档的实际内容

### 导入文档功能

编辑器支持导入多种格式的文档，包括Microsoft Word (.docx)、纯文本 (.txt) 和HTML (.html/.htm) 文件，并将其转换为编辑器可编辑的格式。

#### 技术实现

- 使用mammoth.js库实现.docx文件到HTML的转换
- 通过FileReader API读取.txt和.html文件内容
- 通过"插入"菜单栏中的"导入文档"按钮触发导入功能
- 支持保留文档中的格式，转换为编辑器可编辑的内容
- 文档内容会被直接插入到当前光标位置

#### 支持的文件格式

1. **DOCX文件**
   - 使用mammoth.js转换为HTML
   - 保留基本格式，如文本样式、段落、标题、列表等
   - 转换后插入编辑器中

2. **TXT文件**
   - 将纯文本转换为HTML段落
   - 自动识别换行，每行创建为单独段落
   - 空行会转换为空段落

3. **HTML文件**
   - 直接导入HTML内容到编辑器
   - 保留原始HTML的格式和结构
   - 支持标准HTML和样式

#### 使用方法

1. 点击顶部导航栏中的"插入"选项卡
2. 点击工具栏中的"导入文档"按钮（文件图标）
3. 在弹出的文件选择对话框中选择要导入的文件（支持.docx、.txt、.html、.htm扩展名）
4. 文件将被转换并插入到当前光标位置

#### 注意事项

- 导入Word文档功能基于mammoth.js，支持大多数常见的Word文档格式
- 复杂的Word文档格式（如表格样式、高级图形、页眉页脚等）可能无法完全保留
- 导入TXT文件时会自动转换为段落格式，保留原始文本的换行结构
- 导入HTML文件时，仅支持编辑器能够处理的HTML标签和样式
- 部分复杂HTML结构可能会被编辑器简化或修正
- 导入的图片会作为内联图片保留在文档中
- 导入后可能需要进行一些格式微调
- 建议在导入大文档前先创建备份或在新文档中测试导入功能
- 导入过程完全在浏览器中进行，文件不会上传到服务器

#### 开发说明

导入功能使用以下核心代码实现：
```javascript
// 获取文件扩展名
const fileExt = file.name.split('.').pop()?.toLowerCase();

// 根据文件类型处理内容
if (fileExt === 'docx') {
  // 使用mammoth.js处理Word文档
  const arrayBuffer = e.target?.result as ArrayBuffer;
  const result = await mammoth.convertToHtml({arrayBuffer});
  const html = result.value;
  editor.commands.insertContent(html);
} 
else if (fileExt === 'txt') {
  // 处理纯文本文件
  const text = e.target?.result as string;
  const formattedText = text.split('\n')
    .map(line => line.trim() ? `<p>${line}</p>` : '<p><br></p>')
    .join('');
  editor.commands.insertContent(formattedText);
}
else if (fileExt === 'html') {
  // 直接插入HTML内容
  const html = e.target?.result as string;
  editor.commands.insertContent(html);
}
```

导入文件的文件选择按钮是隐藏的，通过点击可见按钮触发，提供了与Office Word类似的用户体验。导入功能设计遵循了Office Word的风格，以保持整体界面的一致性。

### 打字效果扩展功能

编辑器支持选中文本后通过API获取相关补充文本，并以打字效果动态显示在选中文本后面。

#### 技术实现

- 自定义的TypingEffect扩展，实现文本逐字输入动画效果
- 在气泡菜单中增加"获取补充数据"按钮
- 集成API调用获取与选中文本相关的补充数据（当前使用模拟API）
- 将获取的文本以打字动画方式展示，创造流畅的生成效果

#### 核心特性

- **自定义扩展**: 使用Tiptap Extension API创建了TypingEffect扩展
- **可配置参数**:
  - `typingSpeed`: 打字速度（毫秒），控制每次添加字符的时间间隔
  - `charsPerFrame`: 每帧添加的字符数，可调整为1或更多以控制打字流畅度
- **上下文关联**: 根据选中文本的内容提供相关的补充文本
- **视觉反馈**: 按钮在数据获取过程中显示加载动画

#### 使用方法

1. 在编辑器中选择一段文本
2. 在出现的气泡菜单中点击"获取补充数据"按钮（下载图标）
3. 系统会自动分析选中文本并获取相关数据
4. 获取的文本将以打字动画效果添加到选中文本后面

#### 注意事项

- 选中文本是API获取相关数据的依据，选择不同内容会得到不同补充文本
- 打字速度和每帧字符数可在扩展配置中调整，以获得最佳视觉效果
- 当前实现使用模拟API，可轻松替换为实际后端API
- 此功能最适合需要辅助内容生成或提供补充信息的场景
- 动画效果在性能较差的设备上可能会有轻微卡顿
- 建议配置适当的打字速度和字符数，平衡真实感和用户等待时间

#### 开发说明

TypingEffect扩展是自定义开发的，主要包含以下功能：

- 添加`addTextWithTypingEffect`命令，用于逐字添加文本
- 支持自定义打字速度和每次添加的字符数
- 兼容Tiptap的命令链接口，可与其他命令结合使用
- 支持选择文本后立即应用效果，光标位置自动调整到文本末尾

# Block Editor 自动补全（Autocompletion）功能

## 功能简介
- 支持在编辑器中按 Tab 键触发 AI 自动补全（mock 数据），并以灰色 ghost text 打字动画显示建议内容。
- 再次按 Tab 可接受补全，内容插入到光标处。
- Escape 可取消补全。
- 菜单栏右上角有"自动补全"开关按钮，随时启用/关闭。
- 不影响 block 拖拽图标和 block 结构，不会引入空 block。

## 使用方法
1. 在页面顶部菜单栏，点击"自动补全"按钮切换开关。
2. 在编辑器中输入内容，按 Tab：
   - 第一次 Tab：触发补全，mock AI 返回建议，灰色 ghost text 动画显示。
   - 第二次 Tab：接受补全，内容插入。
   - 按 Escape 可取消 ghost text。
3. 关闭自动补全后，Tab 恢复默认行为。

## 注意事项
- 补全内容为 mock 数据，实际接入 AI 时可替换 `fetchTextData`。
- ghost text 样式通过 `.has-ghost-text[data-ghost]` 实现，灰色、斜体、打字动画，详见 `index.css`。
- 不会导致 block 左侧拖拽图标错位，也不会插入多余空 block。
- 该功能为无侵入式实现，随时可开关。

## 样式自定义
- ghost text 样式可在 `src/styles/index.css` 末尾自定义。

---
如需自定义补全逻辑、动画、AI 接口等，直接修改 `useAutocompletion.ts` 和 `useAutocompletionGhost.ts`。

# AI Suggestion 扩展功能

## 功能介绍
- 分析文档内容，自动标记建议文字，用户可点击建议文字弹出建议框。
- 支持接受/拒绝建议，接受后内容更新，拒绝则移除建议。
- 顶部导航栏右上角新增"样式规则"按钮，可自定义和管理建议样式规则。
- 支持多种建议类型（如拼写检查、翻译等），可自定义规则和样式。

## 使用说明
1. 在编辑器中输入内容，AI Suggestion 扩展会根据规则自动分析并标记建议。
2. 点击被标记的建议文字，会弹出建议框，可选择"接受"或"拒绝"。
3. 点击顶部导航栏"样式规则"按钮，可打开规则配置面板，增删改建议规则和样式。
4. 规则支持自定义标题、描述（Prompt）、颜色。

## 注意事项
- 建议弹窗和样式规则配置均为前端实现，AI建议内容可对接后端或大模型。
- 需确保 @tiptap/core、@tiptap/react、zustand、nanoid 等依赖已安装。
- 当前为基础实现，建议生成和高亮逻辑可根据实际需求扩展。

## 参考
- [Tiptap AI Suggestion 官方文档](https://tiptap.dev/docs/content-ai/capabilities/suggestion/overview)
