import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  XAgentService, 
  ModelConfigManager,
  DEFAULT_MODEL_CONFIG 
} from '../components/AI/services/XAgentService';

// 从本地存储获取API配置
const getStoredAPIConfig = () => {
  // 检查是否在客户端环境
  if (!ModelConfigManager.isClient()) {
    return DEFAULT_MODEL_CONFIG;
  }
  
  try {
    // 优先使用ModelConfigManager获取当前模型配置
    return ModelConfigManager.getCurrentConfig();
  } catch (e) {
    console.error('读取API配置失败:', e);
  }
  return DEFAULT_MODEL_CONFIG;
};

// 创建一个全局XAgentService实例，使用本地存储的配置
const xAgentService = new XAgentService(null, getStoredAPIConfig());

// 全局XAgentService配置更新函数
export const updateXAgentServiceConfig = () => {
  // 检查是否在客户端环境
  if (!ModelConfigManager.isClient()) return;
  
  const config = ModelConfigManager.getCurrentConfig();
  xAgentService.setConfig(config);
};

// 定义消息类型
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  status?: 'loading' | 'success' | 'error';
}

// 定义历史对话类型
export interface ChatHistory {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number; // 创建时间戳
}

// Store 状态接口
interface ChatState {
  conversations: Record<string, ChatHistory>;
  activeConversationId: string | null;
  // 操作方法
  setActiveConversation: (id: string | null) => void;
  addConversation: (title?: string) => string;
  updateConversation: (id: string, title: string, messages: Message[]) => void;
  deleteConversation: (id: string) => void;
  sendMessage: (content: string) => Promise<void>;
  regenerateMessage: (messageId: string) => void;
  // 新增方法
  updateApiConfig: () => void;
}

// 初始对话数据
const initialConversations: Record<string, ChatHistory> = {
  '1': {
    id: '1',
    title: 'Ant Design X 全新升级了什么?',
    messages: [
      {
        id: '1-1',
        role: 'user',
        content: 'Ant Design X 全新升级了什么?',
        status: 'success'
      },
      {
        id: '1-2',
        role: 'assistant',
        content: 'Ant Design X 全新升级了 AI 组件库，包括对话气泡、消息发送器、欢迎组件等多种适用于 AI 应用的界面组件，帮助开发者快速构建高质量的 AI 产品界面。',
        status: 'success'
      }
    ],
    createdAt: Date.now() - 86400000 // 昨天
  },
  '2': {
    id: '2',
    title: 'Ant Design X 组件资产有哪些?',
    messages: [
      {
        id: '2-1',
        role: 'user',
        content: 'Ant Design X 组件资产有哪些?',
        status: 'success'
      },
      {
        id: '2-2',
        role: 'assistant',
        content: 'Ant Design X 提供了丰富的组件资产，主要包括：Bubble对话气泡、Conversations管理对话、Welcome欢迎组件、Prompts提示集、Attachment输入附件、Sender输入框、Suggestion快捷指令、ThoughtChain思维链等组件，以及useXAgent模型调度、useXChat数据管理等工具型Hook。',
        status: 'success'
      }
    ],
    createdAt: Date.now() - 86400000 * 3 // 三天前
  },
  '3': {
    id: '3',
    title: '如何快速安装和引入组件?',
    messages: [
      {
        id: '3-1',
        role: 'user',
        content: '如何快速安装和引入组件?',
        status: 'success'
      },
      {
        id: '3-2',
        role: 'assistant',
        content: '要安装 Ant Design X，只需运行 `npm install @ant-design/x` 或 `yarn add @ant-design/x`。然后在代码中引入需要的组件，例如：`import { Bubble, Sender } from "@ant-design/x"`，就可以开始使用了。',
        status: 'success'
      }
    ],
    createdAt: Date.now() // 今天
  },
  '4': {
    id: '4',
    title: '本次升级的设计节点有哪些?',
    messages: [
      {
        id: '4-1',
        role: 'user',
        content: '本次升级的设计节点有哪些?',
        status: 'success'
      },
      {
        id: '4-2',
        role: 'assistant',
        content: 'Ant Design X 此次升级的设计节点主要包括界面交互优化、响应式布局改进、夜间模式支持、无障碍访问增强以及主题定制系统升级，使开发者能够更灵活地定制AI应用界面。',
        status: 'success'
      }
    ],
    createdAt: Date.now() - 86400000 * 2 // 前天
  },
  '5': {
    id: '5',
    title: 'Markdown测试',
    messages: [
      {
        id: '5-1',
        role: 'user',
        content: '请展示一些Markdown格式的示例',
        status: 'success'
      },
      {
        id: '5-2',
        role: 'assistant',
        content: `# Markdown 演示
## 二级标题

这是一段普通文本，**这是加粗文本**，*这是斜体文本*。

> 这是一段引用文本

### 列表示例
- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

### 代码示例
\`\`\`typescript
import React from 'react';

const Example: React.FC = () => {
  return (
    <div>
      <h1>Hello, Markdown!</h1>
    </div>
  );
};

export default Example;
\`\`\`

### 表格示例
| 名称 | 类型 | 说明 |
| --- | --- | --- |
| name | string | 用户名称 |
| age | number | 用户年龄 |

[这是一个链接](https://ant.design)

![图片描述](https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg)
`,
        status: 'success'
      }
    ],
    createdAt: Date.now() - 86400000 // 一天前
  }
};

// 将Store中的Message转换为XAgentService的Message格式
const convertToXMessages = (messages: Message[]) => {
  return messages.map(msg => ({
    id: msg.id,
    role: msg.role === 'user' ? 'user' : 'assistant' as 'user' | 'assistant' | 'system',
    content: msg.content,
    status: msg.status,
    timestamp: Date.now()
  }));
};

// 创建zustand store
export const useChatStore = create<ChatState>()(
  (ModelConfigManager.isClient() ? 
  persist(
    (set, get) => ({
      conversations: initialConversations,
      activeConversationId: null,
      
      // 设置当前活跃会话
      setActiveConversation: (id) => set({ activeConversationId: id }),
      
      // 添加新会话
      addConversation: (title = '新对话') => {
        const id = Date.now().toString();
        const newConversation: ChatHistory = {
          id,
          title,
          messages: [],
          createdAt: Date.now()
        };
        
        set((state) => ({
          conversations: {
            ...state.conversations,
            [id]: newConversation
          },
          activeConversationId: id
        }));
        
        return id;
      },
      
      // 更新会话
      updateConversation: (id, title, messages) => {
        set((state) => {
          // 确保会话存在
          if (!state.conversations[id]) return state;
          
          return {
            conversations: {
              ...state.conversations,
              [id]: {
                ...state.conversations[id],
                title,
                messages
              }
            }
          };
        });
      },
      
      // 删除会话
      deleteConversation: (id) => {
        set((state) => {
          const newConversations = { ...state.conversations };
          delete newConversations[id];
          
          return {
            conversations: newConversations,
            activeConversationId: state.activeConversationId === id ? null : state.activeConversationId
          };
        });
      },
      
      // 更新API配置
      updateApiConfig: () => {
        // 从ModelConfigManager加载最新配置并更新xAgentService实例
        updateXAgentServiceConfig();
      },
      
      // 发送消息
      sendMessage: async (content) => {
        // 确保使用最新的API配置
        get().updateApiConfig();
      
        const { activeConversationId, conversations } = get();
        
        // 如果没有活跃会话，创建一个新会话
        let currentId = activeConversationId;
        if (!currentId) {
          currentId = get().addConversation();
        }
        
        // 添加用户消息
        const userMessage: Message = {
          id: Date.now().toString(),
          role: 'user',
          content,
          status: 'success'
        };
        
        // 添加助手消息占位
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '',
          status: 'loading'
        };
        
        // 确保当前会话存在，如果不存在则创建初始结构
        let currentConversation = get().conversations[currentId];
        if (!currentConversation) {
          // 如果没有找到会话，可能是状态更新问题，创建一个新的会话
          currentConversation = {
            id: currentId,
            title: '新对话',
            messages: [],
            createdAt: Date.now()
          };
          
          // 先更新会话
          set((state) => ({
            conversations: {
              ...state.conversations,
              [currentId!]: currentConversation
            }
          }));
          
          // 重新获取最新状态
          currentConversation = get().conversations[currentId];
        }
        
        const updatedMessages = [...currentConversation.messages, userMessage, assistantMessage];
        
        // 更新会话标题（使用第一条用户消息作为标题）
        const title = updatedMessages.find(m => m.role === 'user')?.content.slice(0, 30) || '新对话';
        
        // 更新状态
        set((state) => {
          return {
            conversations: {
              ...state.conversations,
              [currentId!]: {
                ...state.conversations[currentId!],
                title,
                messages: updatedMessages
              }
            }
          };
        });
        
        return new Promise<void>((resolve) => {
          try {
            // 转换历史消息为XAgentService格式
            const historyMessages = convertToXMessages(currentConversation.messages);
            
            // 使用流式请求获取回复
            let aiResponse = '';
            
            xAgentService.streamResponse(
              content,
              historyMessages,
              (chunk) => {
                // 每次收到新的内容块时更新消息
                aiResponse += chunk;
                
                set((state) => {
                  const targetConversation = state.conversations[currentId!];
                  // 确保对话存在
                  if (!targetConversation) {
                    return state;
                  }
                  
                  return {
                    conversations: {
                      ...state.conversations,
                      [currentId!]: {
                        ...targetConversation,
                        messages: targetConversation.messages.map(msg => {
                          if (msg.id === assistantMessage.id) {
                            return {
                              ...msg,
                              content: aiResponse,
                              // 一旦开始接收数据，立即将状态设置为success以立刻开始渲染
                              status: 'success'
                            };
                          }
                          return msg;
                        })
                      }
                    }
                  };
                });
              },
              (error) => {
                // 处理错误
                console.error('AI响应错误:', error);
                
                set((state) => {
                  const targetConversation = state.conversations[currentId!];
                  // 确保对话存在
                  if (!targetConversation) {
                    return state;
                  }
                  
                  return {
                    conversations: {
                      ...state.conversations,
                      [currentId!]: {
                        ...targetConversation,
                        messages: targetConversation.messages.map(msg => {
                          if (msg.id === assistantMessage.id) {
                            return {
                              ...msg,
                              content: `消息处理失败: ${error}`,
                              status: 'error'
                            };
                          }
                          return msg;
                        })
                      }
                    }
                  };
                });
                
                resolve(); // 在错误情况下也resolve promise
              },
              () => {
                // 响应完成
                set((state) => {
                  const targetConversation = state.conversations[currentId!];
                  // 确保对话存在
                  if (!targetConversation) {
                    return state;
                  }
                  
                  return {
                    conversations: {
                      ...state.conversations,
                      [currentId!]: {
                        ...targetConversation,
                        messages: targetConversation.messages.map(msg => {
                          if (msg.id === assistantMessage.id) {
                            return {
                              ...msg,
                              status: 'success'
                            };
                          }
                          return msg;
                        })
                      }
                    }
                  };
                });
                
                resolve(); // 响应完成时resolve promise
              }
            );
          } catch (error) {
            console.error('发送消息错误:', error);
            // 更新错误状态
            set((state) => {
              const targetConversation = state.conversations[currentId!];
              // 确保对话存在
              if (!targetConversation) {
                return state;
              }
              
              return {
                conversations: {
                  ...state.conversations,
                  [currentId!]: {
                    ...targetConversation,
                    messages: targetConversation.messages.map(msg => {
                      if (msg.id === assistantMessage.id) {
                        return {
                          ...msg,
                          content: '消息处理失败，请检查网络或API配置',
                          status: 'error'
                        };
                      }
                      return msg;
                    })
                  }
                }
              };
            });
            
            resolve(); // 在错误情况下也resolve promise
          }
        });
      },
      
      // 重新生成消息
      regenerateMessage: async (messageId) => {
        // 确保使用最新的API配置
        get().updateApiConfig();
        
        const { activeConversationId, conversations } = get();
        if (!activeConversationId) return;
        
        const conversation = conversations[activeConversationId];
        if (!conversation) return;
        
        // 找到需要重新生成的消息和对应的用户消息
        let assistantMessageIndex = -1;
        let userMessage = null;
        
        for (let i = 0; i < conversation.messages.length; i++) {
          if (conversation.messages[i].id === messageId) {
            assistantMessageIndex = i;
            // 找到对应的用户消息(通常是前一条)
            if (i > 0 && conversation.messages[i-1].role === 'user') {
              userMessage = conversation.messages[i-1];
            }
            break;
          }
        }
        
        if (assistantMessageIndex === -1 || !userMessage) return;
        
        // 更新消息状态为加载中
        set((state) => {
          const targetConversation = state.conversations[activeConversationId];
          
          return {
            conversations: {
              ...state.conversations,
              [activeConversationId]: {
                ...targetConversation,
                messages: targetConversation.messages.map(msg => {
                  if (msg.id === messageId) {
                    return {
                      ...msg,
                      content: '',
                      status: 'loading'
                    };
                  }
                  return msg;
                })
              }
            }
          };
        });
        
        try {
          // 获取直到用户消息之前的所有历史消息
          const historyMessages = convertToXMessages(
            conversation.messages.slice(0, assistantMessageIndex - 1)
          );
          
          let aiResponse = '';
          
          // 使用XAgentService重新生成回复
          await xAgentService.streamResponse(
            userMessage.content,
            historyMessages,
            (chunk) => {
              // 每次收到新的内容块时更新消息
              aiResponse += chunk;
              
              set((state) => {
                const targetConversation = state.conversations[activeConversationId];
                
                return {
                  conversations: {
                    ...state.conversations,
                    [activeConversationId]: {
                      ...targetConversation,
                      messages: targetConversation.messages.map(msg => {
                        if (msg.id === messageId) {
                          return {
                            ...msg,
                            content: aiResponse,
                            // 一旦开始接收数据，立即将状态设置为success以立刻开始渲染
                            status: 'success'
                          };
                        }
                        return msg;
                      })
                    }
                  }
                };
              });
            },
            (error) => {
              // 处理错误
              set((state) => {
                const targetConversation = state.conversations[activeConversationId];
                
                return {
                  conversations: {
                    ...state.conversations,
                    [activeConversationId]: {
                      ...targetConversation,
                      messages: targetConversation.messages.map(msg => {
                        if (msg.id === messageId) {
                          return {
                            ...msg,
                            content: `重新生成失败: ${error}`,
                            status: 'error'
                          };
                        }
                        return msg;
                      })
                    }
                  }
                };
              });
            },
            () => {
              // 响应完成
              set((state) => {
                const targetConversation = state.conversations[activeConversationId];
                
                return {
                  conversations: {
                    ...state.conversations,
                    [activeConversationId]: {
                      ...targetConversation,
                      messages: targetConversation.messages.map(msg => {
                        if (msg.id === messageId) {
                          return {
                            ...msg,
                            status: 'success'
                          };
                        }
                        return msg;
                      })
                    }
                  }
                };
              });
            }
          );
        } catch (error) {
          console.error('重新生成消息错误:', error);
          // 更新错误状态
          set((state) => {
            const targetConversation = state.conversations[activeConversationId];
            
            return {
              conversations: {
                ...state.conversations,
                [activeConversationId]: {
                  ...targetConversation,
                  messages: targetConversation.messages.map(msg => {
                    if (msg.id === messageId) {
                      return {
                        ...msg,
                        content: '重新生成失败，请检查网络或API配置',
                        status: 'error'
                      };
                    }
                    return msg;
                  })
                }
              }
            };
          });
        }
      }
    }),
    {
      name: 'ai-chat-storage', // localStorage 的 key
      partialize: (state) => ({
        conversations: state.conversations,
        activeConversationId: state.activeConversationId
      }),
    }
  ) : 
  // 服务端渲染时使用非持久化版本
  (set, get) => ({
    conversations: initialConversations,
    activeConversationId: null,

    setActiveConversation: (id) => {
      set({ activeConversationId: id });
    },

    addConversation: (title = '新对话') => {
      const newId = Date.now().toString();
      set((state) => ({
        conversations: {
          ...state.conversations,
          [newId]: {
            id: newId,
            title,
            messages: [],
            createdAt: Date.now()
          }
        },
        activeConversationId: newId
      }));
      return newId;
    },

    updateConversation: (id, title, messages) => {
      set((state) => ({
        conversations: {
          ...state.conversations,
          [id]: {
            ...state.conversations[id],
            title,
            messages
          }
        }
      }));
    },

    deleteConversation: (id) => {
      set((state) => {
        const { [id]: _, ...remainingConversations } = state.conversations;
        
        // 如果当前活跃的对话被删除，需要更新activeConversationId
        let activeId = state.activeConversationId;
        if (activeId === id) {
          // 选择第一个可用的对话作为活跃对话，如果没有则设为null
          const availableIds = Object.keys(remainingConversations);
          activeId = availableIds.length > 0 ? availableIds[0] : null;
        }
        
        return {
          conversations: remainingConversations,
          activeConversationId: activeId
        };
      });
    },

    sendMessage: async (content) => {
      // 确保使用最新的API配置
      get().updateApiConfig();
      
      const { activeConversationId, conversations } = get();
      
      // 如果没有活跃会话，创建一个新会话
      let currentId = activeConversationId;
      if (!currentId) {
        currentId = get().addConversation();
      }
      
      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content,
        status: 'success'
      };
      
      // 添加助手消息占位
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '',
        status: 'loading'
      };
      
      // 确保当前会话存在，如果不存在则创建初始结构
      let currentConversation = get().conversations[currentId];
      if (!currentConversation) {
        // 如果没有找到会话，可能是状态更新问题，创建一个新的会话
        currentConversation = {
          id: currentId,
          title: '新对话',
          messages: [],
          createdAt: Date.now()
        };
        
        // 先更新会话
        set((state) => ({
          conversations: {
            ...state.conversations,
            [currentId!]: currentConversation
          }
        }));
        
        // 重新获取最新状态
        currentConversation = get().conversations[currentId];
      }
      
      const updatedMessages = [...currentConversation.messages, userMessage, assistantMessage];
      
      // 更新会话标题（使用第一条用户消息作为标题）
      const title = updatedMessages.find(m => m.role === 'user')?.content.slice(0, 30) || '新对话';
      
      // 更新对话历史
      set((state) => ({
        conversations: {
          ...state.conversations,
          [currentId!]: {
            ...state.conversations[currentId!],
            title,
            messages: updatedMessages
          }
        }
      }));
      
      // 在服务端渲染中，只是设置一个空的Promise，实际不发送请求
      return Promise.resolve();
    },

    regenerateMessage: (messageId) => {
      // 在服务端渲染环境中，这个方法什么都不做
    },

    updateApiConfig: () => {
      // 在服务端渲染环境中，这个方法什么都不做
    }
  })
)); 