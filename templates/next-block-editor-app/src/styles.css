/* 缩进样式 */
[data-indent="1"] {
  padding-left: 2em;
}
[data-indent="2"] {
  padding-left: 4em;
}
[data-indent="3"] {
  padding-left: 6em;
}
[data-indent="4"] {
  padding-left: 8em;
}
[data-indent="5"] {
  padding-left: 10em;
}

/* 提供视觉指示 */
[data-indent] {
  position: relative;
}
[data-indent]::before {
  content: "";
  position: absolute;
  left: 0.5em;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: rgba(0, 0, 0, 0.1);
}

/* 根据缩进级别调整左边界指示器 */
[data-indent="1"]::before {
  left: 0.5em;
}
[data-indent="2"]::before {
  left: 2.5em;
}
[data-indent="3"]::before {
  left: 4.5em;
}
[data-indent="4"]::before {
  left: 6.5em;
}
[data-indent="5"]::before {
  left: 8.5em;
}

/* 换行符样式 */
.invisible-character {
  display: inline-block;
  color: #a8c7ff;
  user-select: none;
  pointer-events: none;
  opacity: 0.8;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  vertical-align: middle;
}

.invisible-character-paragraph {
  color: #a8c7ff;
  font-size: 16px;
  margin-left: 3px;
  text-shadow: 0 0 1px rgba(168, 199, 255, 0.3);
}

.invisible-character-hard-break {
  color: #a8c7ff;
  font-size: 16px;
  margin-left: 3px;
  text-shadow: 0 0 1px rgba(168, 199, 255, 0.3);
}

/* 批注相关样式 */
.comment-mark {
  background-color: #FFF3CD;
  position: relative;
  border-bottom: 4px dashed #2563EB;
  text-decoration: none;
  padding-bottom: 1px; /* 确保下划线有足够的空间 */
}

/* Z-index管理 */
:root {
  --z-index-base: 1;
  --z-index-comment-container: 5;
  --z-index-comment-marker: 900;
  --z-index-comment-popup: 1000;
  --z-index-dropdown: 1500;
  --z-index-tooltip: 1600;
  --z-index-modal: 1700;
  --z-index-popover: 1800;
  --z-index-toolbar: 2000;
}

/* 批注容器样式 */
.comment-markers-container {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: var(--z-index-comment-container) !important;
}

.comment-marker {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #D32F2F;
  color: white;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  z-index: var(--z-index-comment-marker) !important;
}

.comment-marker:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.comment-bubble {
  transition: opacity 0.2s ease;
  opacity: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: var(--z-index-comment-popup) !important;
}

.comment-bubble .bg-\[#FFF3CD\] {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.comment-bubble .bg-\[#FFF3CD\]:hover {
  background-color: #FFE59E !important;
}

/* 批注修订内容样式 */
.comment-bubble .chevron-icon {
  transition: transform 0.3s ease;
}

.comment-bubble textarea {
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
}

.comment-bubble textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

.comment-bubble button {
  transition: all 0.2s ease;
}

.comment-bubble button:hover:not(:disabled) {
  transform: translateY(-1px);
}

.comment-bubble button:active:not(:disabled) {
  transform: translateY(0);
}

/* 修订内容区域动画 */
.comment-bubble .hidden {
  display: none;
}

.comment-popup {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  animation: fadeIn 0.2s ease-in-out;
  z-index: var(--z-index-comment-popup) !important;
  pointer-events: auto !important;
}

/* 确保菜单栏和工具栏有更高的z-index */
.toolbar, 
.dropdown-content, 
.tippy-box,
.popover,
.menu,
.formatting-toolbar,
.editor-menu,
.dropdown-menu,
.bubble-menu,
.float-menu,
.editor-header,
.navigation-menu {
  z-index: var(--z-index-toolbar) !important;
}

/* 编辑器容器位置 */
.ProseMirror-container,
.ProseMirror-wrapper,
.editor-container {
  position: relative;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 批注内容字体 */
.comment-content {
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
  font-size: 13px;
  line-height: 1.6;
}

/* 批注原文本字体 */
.comment-original {
  font-family: "FangSong", "仿宋", serif;
  font-size: 14px;
  line-height: 1.5;
} 