import React, { useCallback, useEffect, useState } from 'react'
import { Icon } from '@/components/ui/Icon'
import { Toolbar } from '@/components/ui/Toolbar'
import { Editor } from '@tiptap/react'
import ReactDOM from 'react-dom'
import { AIChatPanel } from './AIChatPanel'

export interface AIButtonProps {
  editor: Editor
}

export const AIButton: React.FC<AIButtonProps> = ({ editor }) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null)
  const [isActive, setIsActive] = useState(false)
  const [selectedText, setSelectedText] = useState('')
  const [selection, setSelection] = useState<{from: number, to: number} | null>(null)
  
  // 强制显示所有气泡菜单
  const forceShowBubbleMenus = useCallback(() => {
    // 使用setTimeout确保在其他DOM操作完成后执行
    setTimeout(() => {
      console.log('强制显示气泡菜单')
      const bubbleMenus = document.querySelectorAll('.ProseMirror-BubbleMenu, [data-tippy-root]')
      bubbleMenus.forEach(menu => {
        if (menu instanceof HTMLElement) {
          // 设置多个样式属性以确保显示
          menu.style.visibility = 'visible'
          menu.style.opacity = '1'
          menu.style.display = ''
          menu.classList.remove('hidden')
        }
      })
    }, 100)
  }, [])
  
  // 隐藏所有气泡菜单
  const hideBubbleMenus = useCallback(() => {
    console.log('隐藏气泡菜单')
    const bubbleMenus = document.querySelectorAll('.ProseMirror-BubbleMenu, [data-tippy-root]')
    bubbleMenus.forEach(menu => {
      if (menu instanceof HTMLElement) {
        menu.style.visibility = 'hidden'
        menu.style.opacity = '0'
      }
    })
  }, [])
  
  useEffect(() => {
    // 创建一个portal容器，用于渲染对话框
    const container = document.createElement('div')
    container.className = 'ai-chat-portal'
    document.body.appendChild(container)
    setPortalContainer(container)
    
    return () => {
      document.body.removeChild(container)
      // 确保在组件卸载时恢复气泡菜单的显示
      forceShowBubbleMenus()
    }
  }, [forceShowBubbleMenus])
  
  // 当isActive变为false时，确保气泡菜单显示
  useEffect(() => {
    if (!isActive) {
      forceShowBubbleMenus()
    }
  }, [isActive, forceShowBubbleMenus])
  
  const handleAIClick = useCallback(() => {
    // 隐藏气泡菜单
    hideBubbleMenus()
    
    // 获取选中文本
    const { state } = editor
    const { from, to } = state.selection
    
    if (from === to) {
      return // 没有选择文本
    }
    
    const text = state.doc.textBetween(from, to, ' ')
    
    // 更新状态
    setIsActive(true)
    setSelectedText(text)
    setSelection({ from, to })
  }, [editor, hideBubbleMenus])
  
  const handleClose = useCallback(() => {
    // 关闭对话框
    setIsActive(false)
    
    // 强制显示气泡菜单，延迟一点以确保DOM已更新
    forceShowBubbleMenus()
  }, [forceShowBubbleMenus])
  
  const handleAppendSuggestion = useCallback((text: string) => {
    if (!selection) return
    
    const { from, to } = selection
    
    editor.chain()
      .focus()
      .setTextSelection(to)
      .insertContent(text)
      .run()
    
    // 关闭对话框
    setIsActive(false)
    
    // 延迟一点以确保编辑器状态已更新后再显示气泡菜单
    setTimeout(() => {
      // 手动选择文本以触发气泡菜单显示
      editor.commands.setTextSelection({ from, to: to + text.length })
      // 强制显示气泡菜单
      forceShowBubbleMenus()
    }, 150)
  }, [editor, selection, forceShowBubbleMenus])
  
  const handleReplaceSuggestion = useCallback((text: string) => {
    if (!selection) return
    
    const { from, to } = selection
    
    editor.chain()
      .focus()
      .setTextSelection({ from, to })
      .deleteSelection()
      .insertContent(text)
      .run()
    
    // 关闭对话框
    setIsActive(false)
    
    // 延迟一点以确保编辑器状态已更新后再显示气泡菜单
    setTimeout(() => {
      // 手动选择插入的文本以触发气泡菜单显示
      editor.commands.setTextSelection({ from, to: from + text.length })
      // 强制显示气泡菜单
      forceShowBubbleMenus()
    }, 150)
  }, [editor, selection, forceShowBubbleMenus])
  
  return (
    <>
      <Toolbar.Button
        className="text-purple-500 hover:text-purple-600 active:text-purple-600 dark:text-purple-400 dark:hover:text-purple-300 dark:active:text-purple-400"
        activeClassname="text-purple-600 hover:text-purple-600 dark:text-purple-400 dark:hover:text-purple-200"
        onClick={handleAIClick}
      >
        <Icon name="Bot" className="mr-1" />
        AI助手
      </Toolbar.Button>
      
      {portalContainer && isActive && ReactDOM.createPortal(
        <AIChatPanel 
          editor={editor}
          selectedText={selectedText}
          onClose={handleClose}
          onAppend={handleAppendSuggestion}
          onReplace={handleReplaceSuggestion}
        />,
        portalContainer
      )}
    </>
  )
} 