import React, { useState, useEffect, useLayoutEffect, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { Sender, Prompts } from '@ant-design/x'
import { Surface } from '@/components/ui/Surface'
import { Button } from '@/components/ui/Button'
import { Icon } from '@/components/ui/Icon'
import { useXAgentService } from '@/components/AI/services/XAgentService'

// 对话框的固定尺寸常量
const DIALOG_WIDTH = 400
const DIALOG_HEIGHT = 350  // 调整高度，移除标题栏的高度
const MESSAGE_LIST_HEIGHT = 250  // 调整消息区域高度

// 预设的提示集
const promptItems = [
  {
    key: 'rewrite',
    label: '改写',
    // description: '使文本更加专业或易读',
    icon: <Icon name="Pencil" className="w-4 h-4" />,
  },
  {
    key: 'polish',
    label: '润色',
    // description: '改善语法和表达',
    icon: <Icon name="Sparkles" className="w-4 h-4" />,
  },
  {
    key: 'summarize',
    label: '总结',
    // description: '提取关键信息和要点',
    icon: <Icon name="List" className="w-4 h-4" />,
  },
  {
    key: 'expand',
    label: '扩展',
    // description: '添加更多细节或示例',
    icon: <Icon name="Plus" className="w-4 h-4" />,
  },
  // {
  //   key: 'translate',
  //   label: '翻译',
  //   // description: '转换为其他语言',
  //   icon: <Icon name="Globe" className="w-4 h-4" />,
  // },
  // {
  //   key: 'format',
  //   label: '格式化',
  //   // description: '调整布局和结构',
  //   icon: <Icon name="AlignLeft" className="w-4 h-4" />,
  // },
];

export interface AIChatPanelProps {
  editor: Editor
  selectedText: string
  onClose: () => void
  onAppend: (text: string) => void
  onReplace: (text: string) => void
}

export const AIChatPanel: React.FC<AIChatPanelProps> = ({ 
  editor, 
  selectedText, 
  onClose,
  onAppend,
  onReplace 
}) => {
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant' | 'system', content: string }>>([])
  const [position, setPosition] = useState<{ top: number; left: number; isAbove: boolean }>({ top: 0, left: 0, isAbove: false })
  const dialogRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false)
  const senderRef = useRef<HTMLDivElement>(null)
  
  // 初始化XAgentService
  const xagentService = useXAgentService()
  
  // 设置适合编辑器场景的系统提示词
  useEffect(() => {
    xagentService.setSystemPrompt(
      `你是TipTap编辑器的AI助手，用于帮助用户处理文本内容。
      用户选择了以下文本: "${selectedText}"
      尽量提供有帮助、精确、简明的回复。根据用户的请求，可以针对选中文本提供改写、润色、总结、扩展等建议。
      回复保持专业且友好，不要太长。`
    )
  }, [selectedText, xagentService])
  
  // 自动滚动到最新消息
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // 计算对话框位置的逻辑
  useLayoutEffect(() => {
    const calculatePosition = () => {
      // 获取选中文本的位置
      const selection = editor.state.selection
      const { from, to } = selection
      const editorView = editor.view
      
      // 获取选中文本的起始和结束位置坐标
      const startCoords = editorView.coordsAtPos(from)
      const endCoords = editorView.coordsAtPos(to)
      
      // 获取视窗高度和宽度
      const viewportHeight = window.innerHeight
      const viewportWidth = window.innerWidth
      
      // 计算选中文本的中心位置
      const centerX = (startCoords.left + endCoords.right) / 2
      
      // 对话框的左侧位置，确保不超出视窗
      let left = Math.max(10, Math.min(centerX - DIALOG_WIDTH / 2, viewportWidth - DIALOG_WIDTH - 10))
      
      // 检查下方空间是否足够
      const bottomSpace = viewportHeight - endCoords.bottom
      const topSpace = startCoords.top
      
      // 是否应该显示在上方
      const shouldShowAbove = bottomSpace < DIALOG_HEIGHT && topSpace > DIALOG_HEIGHT
      
      // 根据显示位置计算top值
      let top = 0
      if (shouldShowAbove) {
        // 显示在选中文本上方，留出间距
        top = startCoords.top - DIALOG_HEIGHT - 10
      } else {
        // 显示在选中文本下方，留出间距
        top = endCoords.bottom + 10
      }
      
      // 确保对话框不会超出视窗顶部或底部
      if (top < 10) top = 10
      if (top + DIALOG_HEIGHT > viewportHeight - 10) top = viewportHeight - DIALOG_HEIGHT - 10
      
      // 更新位置状态
      setPosition({ top, left, isAbove: shouldShowAbove })
    }
    
    // 初始计算位置
    calculatePosition()
    
    // 监听窗口大小变化，重新计算位置
    window.addEventListener('resize', calculatePosition)
    return () => {
      window.removeEventListener('resize', calculatePosition)
    }
  }, [editor])

  // 聊天历史中添加系统消息
  useEffect(() => {
    setMessages([
      { role: 'assistant', content: `我是AI助手，您选择了以下文本：\n\n"${selectedText}"\n\n您想对这段文本做什么操作？我可以帮您润色、改写、总结或扩展这段内容。` }
    ])
    // 重置用户发送消息状态
    setHasUserSentMessage(false)
  }, [selectedText])

  // 处理提示集选项点击
  const handlePromptClick = (info: { data: any }) => {
    const promptText = info.data.label;
    handleSendMessage(promptText);
  };

  // 发送消息
  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return

    // 标记用户已发送消息
    setHasUserSentMessage(true)
    
    // 添加用户消息
    setMessages(prev => [...prev, { role: 'user', content: message }])
    setInputValue('')
    setLoading(true)

    try {
      // 创建一个包含历史消息的数组，用于发送到AI服务
      const formattedMessages = messages.map(msg => ({
        id: Math.random().toString(36).substring(2, 15),
        role: msg.role,
        content: msg.content,
        timestamp: Date.now()
      }));

      // 调用XAgentService获取流式响应
      let response = '';
      
      // 使用流式响应
      await xagentService.streamResponse(
        message,
        formattedMessages,
        (chunk) => {
          // 增量更新响应
          response += chunk;
          
          // 如果是第一个响应块，隐藏加载状态
          if (response.length === chunk.length) {
            setLoading(false);
          }
          
          // 更新消息，显示正在输入的响应
          setMessages(prev => {
            const newMessages = [...prev];
            // 如果已经有助手消息，则更新它
            if (newMessages.length > 0 && newMessages[newMessages.length - 1].role === 'assistant') {
              newMessages[newMessages.length - 1].content = response;
            } else {
              // 否则添加新消息
              newMessages.push({ role: 'assistant', content: response });
            }
            return newMessages;
          });
        },
        (error) => {
          console.error('AI响应错误:', error);
          // 添加错误消息
          setMessages(prev => [...prev, { 
            role: 'assistant', 
            content: `抱歉，处理您的请求时出现了问题: ${error}` 
          }]);
          setLoading(false);
        },
        () => {
          // 完成回调
          setLoading(false);
        }
      );
    } catch (error) {
      console.error('发送消息错误:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: '抱歉，处理您的请求时出现了问题，请稍后再试。' 
      }]);
      setLoading(false);
    }
  }

  // 处理操作按钮点击
  const handleAppend = () => {
    const aiResponse = messages[messages.length - 1]?.content || ''
    if (aiResponse) {
      onAppend(aiResponse)
    }
  }

  const handleReplace = () => {
    const aiResponse = messages[messages.length - 1]?.content || ''
    if (aiResponse) {
      onReplace(aiResponse)
    }
  }

  const handleRegenerate = () => {
    const userMessage = messages.find(m => m.role === 'user')?.content || ''
    if (userMessage) {
      // 移除最后一条助手消息
      setMessages(prev => prev.slice(0, prev.length - 1))
      handleSendMessage(userMessage)
    }
  }

  const handleDiscard = () => {
    onClose()
  }

  // 检查是否应该显示操作按钮区域
  const shouldShowActionButtons = hasUserSentMessage && messages.length > 1 && messages[messages.length - 1].role === 'assistant' && !loading;
  
  // 检查是否应该显示提示集
  const shouldShowPrompts = (!loading && messages.length <= 1) || 
                           (messages.length === 2 && messages[1].role === 'user' && loading);

  // 在组件挂载和卸载时添加/移除点击事件监听
  useEffect(() => {
    // 处理点击事件的函数
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    // 添加点击事件监听器
    document.addEventListener('mousedown', handleClickOutside);
    
    // 在组件卸载时移除事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div
      ref={dialogRef}
      className="ai-chat-dialog fixed z-50 shadow-lg rounded-lg bg-white dark:bg-gray-800 overflow-hidden flex flex-col"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: `${DIALOG_WIDTH}px`,
        height: `${DIALOG_HEIGHT}px`, // 设置固定高度
        border: '1px solid #e2e8f0',
        filter: 'drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1))',
      }}
    >
      {/* 如果对话框在选中文本上方，则在底部添加箭头 */}
      {position.isAbove && (
        <div 
          className="absolute bottom-0 left-1/2 w-4 h-4 bg-white dark:bg-gray-800 transform translate-y-1/2 rotate-45 -ml-2 border-r border-b border-gray-200 dark:border-gray-700"
          style={{ zIndex: -1 }}
        />
      )}
      
      {/* 如果对话框在选中文本下方，则在顶部添加箭头 */}
      {!position.isAbove && (
        <div 
          className="absolute top-0 left-1/2 w-4 h-4 bg-white dark:bg-gray-800 transform -translate-y-1/2 rotate-45 -ml-2 border-l border-t border-gray-200 dark:border-gray-700"
          style={{ zIndex: -1 }}
        />
      )}
      
      {/* 消息列表区域 - 固定高度，允许滚动 */}
      <div 
        className="flex-1 overflow-y-auto p-3 space-y-4" 
        style={{ 
          height: `${MESSAGE_LIST_HEIGHT}px`,
          maxHeight: `${MESSAGE_LIST_HEIGHT}px`,
          overflowY: 'auto',
          overflowX: 'hidden'
        }}
      >
        {messages.map((message, index) => (
          <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div
              className={`max-w-[80%] p-2 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
              }`}
            >
              <div style={{ 
                whiteSpace: 'pre-wrap', 
                fontSize: '14px', 
                lineHeight: '1.3',
                letterSpacing: '-0.01em'
              }}>{message.content}</div>
            </div>
          </div>
        ))}
        
        {loading && (
          <div className="flex justify-start">
            <div className="max-w-[80%] p-3 rounded-lg bg-gray-100 dark:bg-gray-700">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse delay-100"></div>
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse delay-200"></div>
              </div>
            </div>
          </div>
        )}
        
        {/* 添加一个空的div作为滚动的目标位置 */}
        <div ref={messagesEndRef} />
      </div>

      {/* 提示集区域 - 当用户未发送消息时显示 */}
      {shouldShowPrompts && (
        <div className="dark:border-gray-700 flex-shrink-0" style={{ height: '42px', overflow: 'hidden' }}>
          <Prompts 
            items={promptItems}
            onItemClick={handlePromptClick}
            wrap
            styles={{
              list: { padding: '6px 8px' },
              item: { 
                // margin: '2px', 
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                padding: '3px 6px'
              },
              itemContent: { 
                fontSize: '12px', 
                lineHeight: '1.2',
                padding: '0'
              }
            }}
          />
        </div>
      )}

      {/* 操作按钮区域 - 当用户已发送消息且有AI回复时显示 */}
      {shouldShowActionButtons && (
        <div className="flex justify-end gap-2 p-2 dark:border-gray-700 flex-shrink-0" style={{ height: '42px' }}>
          <Button variant="ghost" onClick={handleDiscard}>
            <Icon name="X" className="w-4 h-4 mr-1" />
            舍弃
          </Button>
          <Button variant="ghost" onClick={handleRegenerate}>
            <Icon name="RefreshCw" className="w-4 h-4 mr-1" />
            重做
          </Button>
          <Button variant="ghost" onClick={handleReplace}>
            <Icon name="Replace" className="w-4 h-4 mr-1" />
            替代
          </Button>
          <Button variant="primary" onClick={handleAppend}>
            <Icon name="Plus" className="w-4 h-4 mr-1" />
            追加
          </Button>
        </div>
      )}

      {/* 输入区域 - 固定高度 */}
      <div 
        ref={senderRef}
        className="p-2 dark:border-gray-700 flex-shrink-0" 
        style={{ height: '60px', marginBottom: '10px',marginTop: '0px' }}
        tabIndex={-1}
      >
        <Sender
          loading={loading}
          autoSize={{ minRows: 1, maxRows: 1 }}
          submitType="enter"
          value={inputValue}
          onChange={setInputValue}
          onSubmit={handleSendMessage}
          placeholder="请描述您想对选中文本进行什么操作..."
          actions={(originalActions, { components }) => (
            <Button 
              variant="primary" 
              onClick={() => {
                if (inputValue.trim()) {
                  handleSendMessage(inputValue)
                }
              }}
              disabled={loading}
            >
              <Icon name="Send" className="w-4 h-4" />
            </Button>
          )}
        />
      </div>
    </div>
  )
} 