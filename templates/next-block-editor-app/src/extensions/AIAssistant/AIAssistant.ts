import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'
import { AIChatPanel } from './components/AIChatPanel'

export interface AIAssistantOptions {
  aiPromptCallback?: (text: string) => Promise<{ content: string, success: boolean }>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    aiAssistant: {
      /**
       * 显示AI助手对话框
       */
      showAIAssistant: () => ReturnType,
      /**
       * 关闭AI助手对话框
       */
      closeAIAssistant: () => ReturnType,
      /**
       * 应用AI助手建议（追加到选中文本后）
       */
      appendAISuggestion: (text: string) => ReturnType,
      /**
       * 替换选中文本为AI助手建议
       */
      replaceWithAISuggestion: (text: string) => ReturnType,
    }
  }
}

export const AIAssistant = Extension.create<AIAssistantOptions>({
  name: 'aiAssistant',

  addOptions() {
    return {
      aiPromptCallback: async () => ({ content: '', success: false }),
    }
  },

  addStorage() {
    return {
      isActive: false,
      selection: { from: 0, to: 0 },
      selectedText: '',
      aiResponse: '',
      isLoading: false,
      error: null,
    }
  },

  addCommands() {
    return {
      showAIAssistant: () => ({ state, dispatch }) => {
        const { selection } = state
        const { from, to } = selection
        
        if (from === to) {
          return false
        }
        
        const selectedText = state.doc.textBetween(from, to, ' ')
        
        this.storage.isActive = true
        this.storage.selection = { from, to }
        this.storage.selectedText = selectedText
        
        // 触发编辑器更新，使用一个空对象作为事件细节
        this.editor.emit('update', {})
        
        return true
      },
      
      closeAIAssistant: () => () => {
        this.storage.isActive = false
        this.storage.aiResponse = ''
        
        // 触发编辑器更新，使用一个空对象作为事件细节
        this.editor.emit('update', {})
        
        return true
      },
      
      appendAISuggestion: (text) => ({ chain, state }) => {
        const { from, to } = this.storage.selection
        
        return chain()
          .focus()
          .setTextSelection(to)
          .insertContent(text)
          .run()
      },
      
      replaceWithAISuggestion: (text) => ({ chain }) => {
        const { from, to } = this.storage.selection
        
        return chain()
          .focus()
          .setTextSelection({ from, to })
          .deleteSelection()
          .insertContent(text)
          .run()
      }
    }
  },
  
  addProseMirrorPlugins() {
    const pluginKey = new PluginKey('aiAssistant')
    
    return [
      new Plugin({
        key: pluginKey,
        props: {
          handleDOMEvents: {
            click: (view, event) => {
              // 如果AI助手面板打开且点击事件不在面板内，关闭面板
              if (this.storage.isActive) {
                const target = event.target as HTMLElement
                const isClickInsidePanel = !!target.closest('.ai-chat-dialog')
                
                if (!isClickInsidePanel) {
                  this.editor.commands.closeAIAssistant()
                }
              }
              
              return false
            },
          },
        },
      }),
    ]
  },
}) 