# AI辅助扩展功能

这个扩展功能为TipTap编辑器添加了AI辅助功能，允许用户选择文本后进行AI对话并应用生成的内容。

## 功能介绍

- **选择文本触发**: 用户选择编辑器中的文本后，可以点击气泡菜单中的AI助手按钮
- **对话式界面**: 弹出对话框中可以与AI进行交互对话
- **多种操作**: 支持将AI生成的内容追加、替换原文本、重新生成或舍弃
- **实时反馈**: 即时展示AI响应，并提供多种处理选项
- **智能位置计算**: 根据可用空间自动选择在文本上方或下方显示对话框，避免遮挡选中文本
- **交互优化**: 激活AI对话框时自动隐藏气泡菜单，任何操作关闭对话框后自动恢复气泡菜单
- **大模型集成**: 通过XAgentService与OpenAI、DeepSeek等大模型API集成，提供真实AI能力

## 实现细节

### 组件结构

- `AIButton`: 在气泡菜单中显示的AI助手按钮组件
- `AIChatPanel`: AI对话面板组件，包含消息列表和输入区域

### 实现方式

本功能没有使用TipTap扩展的形式，而是采用了React组件的方式实现：

1. 在气泡菜单中添加`AIButton`组件
2. 当点击按钮时，获取当前选中的文本内容
3. 使用Portal在文档中创建对话框，智能计算并定位在选中文本的上方或下方
4. 通过Ant Design X的`Sender`组件实现对话功能
5. 用户可以对生成的内容进行追加、替换、重做或舍弃操作
6. 在对话框激活时自动隐藏气泡菜单，关闭时恢复显示
7. 通过XAgentService向配置的AI模型发送请求并处理流式响应

### 位置计算逻辑

对话框位置采用了智能计算逻辑：

1. 获取选中文本的开始和结束位置坐标
2. 计算视窗可用空间，确定对话框的可用高度和宽度
3. 判断下方空间是否足够放置对话框，不足时则选择在上方显示
4. 确保对话框水平居中于选中文本，并防止超出视窗边界
5. 根据显示位置添加合适的箭头指示器，指向选中的文本
6. 监听窗口大小变化事件，自动重新计算位置

### 菜单控制逻辑

为了提供更好的用户体验，实现了以下气泡菜单控制逻辑：

1. 使用集中式管理的`hideBubbleMenus`和`showBubbleMenus`函数处理菜单状态
2. 通过`useEffect`钩子监听对话框的`isActive`状态变化，确保对话框关闭时恢复气泡菜单
3. 在组件卸载时自动恢复菜单显示，防止菜单永久消失
4. 使用事件监听确保不同情况下（追加文本、替换文本、舍弃操作等）都能正确恢复菜单状态
5. 通过CSS的`visibility`和`opacity`属性控制菜单显隐，提供平滑过渡效果

### AI服务集成

通过XAgentService实现与大型语言模型的集成：

1. 使用`useXAgentService` Hook获取服务实例
2. 根据选中文本设置自定义的系统提示词，引导AI更好地处理文本编辑任务
3. 利用流式响应(streaming)实现实时显示AI回复，提升用户体验
4. 支持多种AI提供商(OpenAI、DeepSeek、Anthropic等)，可通过配置灵活切换
5. 包含完善的错误处理和加载状态显示

### 使用的依赖

- `@tiptap/react`: TipTap编辑器核心
- `@ant-design/x`: Ant Design X组件库，特别是Sender组件和XAgentService
- React Hooks: 用于状态管理和副作用处理
- React Portal: 用于在DOM中任意位置渲染组件

## 注意事项

1. **使用方式**: 必须先选择文本，才能激活AI助手功能
2. **API密钥**: 使用前需要在配置中设置有效的API密钥
3. **样式兼容性**: 对话框样式使用了TailwindCSS，与编辑器其他部分保持一致
4. **定位适配**: 对话框会根据视窗大小和选中文本位置自动调整位置，确保良好的用户体验
5. **性能考虑**: 使用React.memo、useCallback和useLayoutEffect优化组件渲染和定位性能
6. **DOM查询依赖**: 气泡菜单控制逻辑依赖于DOM查询，如果TipTap更新类名可能需要调整查询选择器
7. **流量控制**: 使用流式响应可能会产生更多的API调用流量，注意控制使用成本
8. **状态恢复**: 菜单状态管理使用React状态和效果钩子，确保在各种场景下都能正确恢复UI状态

## 配置指南

要使用AI功能，需要先配置API密钥：

1. 打开编辑器设置面板
2. 进入"AI配置"选项卡
3. 选择要使用的AI服务提供商(OpenAI/DeepSeek等)
4. 输入有效的API密钥
5. 保存设置

## 未来改进方向

- 支持附件和图片处理
- 增加更多文本处理专用指令
- 添加历史记录功能
- 优化位置计算算法，考虑更多边缘情况
- 添加对移动设备的更好支持
- 增加更多自定义交互动画
- 改进菜单控制机制，减少DOM查询依赖
- 支持跨文档片段的操作 