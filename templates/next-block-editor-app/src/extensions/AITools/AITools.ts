import { Extension } from '@tiptap/core'
import { applyTypingEffect } from '@/lib/api/typingEffectHelper'
import { XAgentService, ModelConfigManager } from '@/components/AI/services/XAgentService'

export interface AIToolsOptions {
  // 打字速度 (ms)
  typingSpeed: number
  // 每次添加字符的数量
  charsPerFrame: number
}

// AI工具的选项类型
export type AIToolType = 'rewrite' | 'expand' | 'summarize';

// AI工具对应的提示词
export const AI_TOOL_PROMPTS: Record<AIToolType, string> = {
  rewrite: '请改写以下文本，保持原意但使用不同的表达方式：',
  expand: '请基于以下文本进行扩展和丰富，增加更多细节和内容：',
  summarize: '请总结以下文本的要点，用简洁的语言表达主要内容：'
};

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    aiTools: {
      /**
       * 使用AI工具处理选中文本
       * @param toolType 工具类型：改写、扩展或总结
       * @param onComplete 可选的完成回调
       */
      applyAITool: (
        toolType: AIToolType, 
        onComplete?: () => void
      ) => ReturnType
    }
  }
}

export const AITools = Extension.create<AIToolsOptions>({
  name: 'aiTools',

  addOptions() {
    return {
      typingSpeed: 50,
      charsPerFrame: 1,
    }
  },

  addCommands() {
    return {
      applyAITool: (toolType: AIToolType, onComplete?: () => void) => ({ editor }) => {
        if (!editor) {
          return false;
        }

        // 获取选中文本
        const { from, to } = editor.state.selection
        if (from === to) {
          // 没有选中文本
          return false;
        }

        // 获取选中的文本内容
        const selectedText = editor.state.doc.textBetween(from, to)
        if (!selectedText) {
          return false;
        }

        // 创建XAgentService实例
        const service = new XAgentService(null, ModelConfigManager.getCurrentConfig());
        
        // 根据工具类型构建提示词
        const prompt = `${AI_TOOL_PROMPTS[toolType]}\n\n${selectedText}`;

        // 设置加载状态（可选：在UI上显示加载状态）
        
        // 调用AI服务处理文本
        service.streamResponse(
          prompt,
          [],
          (chunk) => {
            // 这里不做任何处理，等待完整响应后一次性应用
          },
          (error) => {
            console.error('AI处理失败:', error);
            // 可以在这里处理错误，例如显示错误提示
          },
          () => {
            // 流式响应完成，此时应用打字效果
            service.sendMessage(prompt, [])
              .then(({ response, error }) => {
                if (error) {
                  console.error('AI服务错误:', error);
                  return;
                }
                
                if (toolType === 'rewrite') {
                  // 对于改写，替换原文本
                  editor.commands.deleteSelection();
                  
                  // 使用打字效果添加新文本
                  applyTypingEffect(
                    editor, 
                    response, 
                    this.options.typingSpeed, 
                    this.options.charsPerFrame,
                    onComplete
                  );
                } else {
                  // 对于扩展和总结，在原文本后添加
                  // 先移动光标到文本末尾
                  editor.commands.setTextSelection(to);
                  
                  // 添加分隔符和换行
                  editor.commands.insertContent('\n\n');
                  
                  // 使用打字效果添加AI生成的文本
                  applyTypingEffect(
                    editor, 
                    response, 
                    this.options.typingSpeed, 
                    this.options.charsPerFrame,
                    onComplete
                  );
                }
              });
          }
        );
        
        return true;
      },
    };
  },
}); 