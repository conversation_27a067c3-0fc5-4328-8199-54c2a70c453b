import { Extension } from '@tiptap/core'
import { Decoration, DecorationSet } from 'prosemirror-view'
import { Plugin, PluginKey } from 'prosemirror-state'

export interface GhostTextOptions {
  getGhost: () => { text: string; pos: number } | null
  onClearGhost?: () => void
}

export const GhostText = Extension.create<GhostTextOptions>({
  name: 'ghostText',

  addOptions() {
    return {
      getGhost: () => null,
      onClearGhost: undefined,
    }
  },

  addProseMirrorPlugins() {
    const pluginKey = new PluginKey('ghostText')
    return [
      new Plugin({
        key: pluginKey,
        state: {
          init: () => DecorationSet.empty,
          apply(tr, old) {
            return tr.getMeta(pluginKey) || old
          },
        },
        props: {
          decorations: (state) => {
            const ghost = this.options.getGhost()
            if (!ghost || !ghost.text || typeof ghost.pos !== 'number') return null
            // 只在pos处插入inline widget
            return DecorationSet.create(state.doc, [
              Decoration.widget(
                ghost.pos,
                () => {
                  const span = document.createElement('span')
                  span.textContent = ghost.text
                  span.style.opacity = '0.6'
                  span.style.color = '#bdbdbd'
                  span.style.fontStyle = 'italic'
                  span.style.pointerEvents = 'none'
                  span.className = 'ghost-text-widget'
                  return span
                },
                { side: 0 }
              ),
            ])
          },
          handleKeyDown: (view, event) => {
            if (event.key === 'Escape' && this.options.onClearGhost) {
              this.options.onClearGhost()
              view.dispatch(view.state.tr.setSelection(view.state.selection))
              return true
            }
            return false
          },
        },
      }),
    ]
  },
}) 