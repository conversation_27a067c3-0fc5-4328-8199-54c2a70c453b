import { Extension } from '@tiptap/core';
import { Node as ProseMirrorNode } from 'prosemirror-model';
import { Transaction } from 'prosemirror-state';
import { findParentNode } from '@tiptap/core';

// 添加类型声明，使indent和outdent命令可用
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    indent: {
      /**
       * 增加当前文本块的缩进级别
       */
      indent: () => ReturnType,
      /**
       * 减少当前文本块的缩进级别
       */
      outdent: () => ReturnType,
    }
  }
}

// 修改缩进行为的扩展
export const Indent = Extension.create({
  name: 'indent',

  addOptions() {
    return {
      types: ['paragraph', 'heading', 'blockquote', 'listItem'],
      minIndent: 0,
      maxIndent: 5,
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          indent: {
            default: 0,
            parseHTML: element => {
              const indent = element.getAttribute('data-indent');
              return indent ? parseInt(indent, 10) : 0;
            },
            renderHTML: attributes => {
              if (attributes.indent === 0) {
                return {};
              }
              return { 'data-indent': attributes.indent };
            },
          },
        },
      },
    ];
  },

  onUpdate() {
    // 更新时什么都不做
  },

  addKeyboardShortcuts() {
    return {
      Tab: () => this.editor.commands.indent(),
      'Shift-Tab': () => this.editor.commands.outdent(),
    };
  },
  
  addCommands() {
    return {
      indent: () => ({ tr, state, dispatch }: { tr: Transaction, state: any, dispatch: any }) => {
        try {
          const { selection } = state;
          const { from, to } = selection;

          // 获取当前光标所在的段落节点
          const currentNode = findParentNode(node => 
            this.options.types.includes(node.type.name)
          )(selection);

          if (!currentNode) {
            return false;
          }

          // 处理单个节点的缩进
          if (currentNode) {
            const node = currentNode.node;
            const pos = currentNode.pos;
            const currentIndent = node.attrs.indent || 0;
            
            if (currentIndent < this.options.maxIndent) {
              const newIndent = currentIndent + 1;

              const transaction = tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                indent: newIndent,
              });

              if (dispatch) {
                dispatch(transaction);
              }
              
              return true;
            }
          }

          return false;
        } catch (error) {
          return false;
        }
      },

      outdent: () => ({ tr, state, dispatch }: { tr: Transaction, state: any, dispatch: any }) => {
        try {
          const { selection } = state;
          const { from, to } = selection;

          // 获取当前光标所在的段落节点
          const currentNode = findParentNode(node => 
            this.options.types.includes(node.type.name)
          )(selection);

          if (!currentNode) {
            return false;
          }

          // 处理单个节点的缩进
          if (currentNode) {
            const node = currentNode.node;
            const pos = currentNode.pos;
            const currentIndent = node.attrs.indent || 0;
            
            if (currentIndent > this.options.minIndent) {
              const newIndent = currentIndent - 1;

              const transaction = tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                indent: newIndent,
              });

              if (dispatch) {
                dispatch(transaction);
              }
              
              return true;
            }
          }

          return false;
        } catch (error) {
          return false;
        }
      },
    };
  },
}); 