'use client'

import { Extension } from '@tiptap/core'
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import { Editor } from '@tiptap/react'

export interface InvisibleCharactersOptions {
  visible: boolean
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    invisibleCharacters: {
      /**
       * 切换显示/隐藏换行符
       */
      toggleInvisibleCharacters: () => ReturnType,
      /**
       * 显示换行符
       */
      showInvisibleCharacters: () => ReturnType,
      /**
       * 隐藏换行符
       */
      hideInvisibleCharacters: () => ReturnType,
    }
  }
}

export const InvisibleCharacters = Extension.create<InvisibleCharactersOptions>({
  name: 'invisibleCharacters',

  addOptions() {
    return {
      visible: false,
    }
  },

  addStorage() {
    return {
      isVisible: this.options.visible,
    }
  },

  addCommands() {
    return {
      toggleInvisibleCharacters: () => ({ editor }) => {
        // 获取当前状态的反值
        const isVisible = !editor.storage.invisibleCharacters.isVisible
        
        // 更新存储状态
        editor.storage.invisibleCharacters.isVisible = isVisible
        
        // 触发视图更新以应用新的装饰器
        editor.view.dispatch(editor.state.tr.setMeta('forceUpdate', true))
        
        // 手动触发DOM更新
        setTimeout(() => {
          editor.view.updateState(editor.view.state)
        }, 0)
        
        return true
      },
      showInvisibleCharacters: () => ({ editor }) => {
        editor.storage.invisibleCharacters.isVisible = true
        editor.view.dispatch(editor.state.tr.setMeta('forceUpdate', true))
        
        // 手动触发DOM更新
        setTimeout(() => {
          editor.view.updateState(editor.view.state)
        }, 0)
        
        return true
      },
      hideInvisibleCharacters: () => ({ editor }) => {
        editor.storage.invisibleCharacters.isVisible = false
        editor.view.dispatch(editor.state.tr.setMeta('forceUpdate', true))
        
        // 手动触发DOM更新
        setTimeout(() => {
          editor.view.updateState(editor.view.state)
        }, 0)
        
        return true
      },
    }
  },

  addProseMirrorPlugins() {
    const { editor } = this

    return [
      new Plugin({
        key: new PluginKey('invisibleCharacters'),
        appendTransaction: (transactions, oldState, newState) => {
          // 检查事务是否包含forceUpdate元数据
          const forceUpdate = transactions.some(tr => tr.getMeta('forceUpdate'))
          if (forceUpdate) {
            // 返回一个空事务以触发更新
            return newState.tr
          }
          return null
        },
        props: {
          decorations(state) {
            // 确保editor和存储已正确初始化
            if (!editor || !editor.storage.invisibleCharacters) {
              return null
            }

            // 根据存储中的状态决定是否显示装饰器
            if (!editor.storage.invisibleCharacters.isVisible) {
              return null
            }

            const decorations: Decoration[] = []
            const { doc } = state

            // 遍历文档中的所有段落和硬换行
            doc.descendants((node, pos) => {
              // 处理段落结束
              if (node.type.name === 'paragraph' && pos > 0) {
                decorations.push(
                  Decoration.widget(pos + node.nodeSize - 1, () => {
                    const span = document.createElement('span')
                    span.textContent = '↵'
                    span.className = 'invisible-character invisible-character-paragraph'
                    span.style.color = 'rgb(157 159 175)'
                    span.style.fontSize = '16px'
                    return span
                  }, { side: 1 })
                )
              }

              // 处理硬换行
              if (node.type.name === 'hardBreak') {
                decorations.push(
                  Decoration.widget(pos + node.nodeSize, () => {
                    const span = document.createElement('span')
                    span.textContent = '↵'
                    span.className = 'invisible-character invisible-character-hard-break'
                    return span
                  }, { side: 1 })
                )
              }
            })

            return DecorationSet.create(doc, decorations)
          },
        },
      }),
    ]
  },
}) 