'use client'

import { HocuspocusProvider } from '@hocuspocus/provider'

import { API } from '@/lib/api'

import {
  BlockquoteFigure,
  CharacterCount,
  CodeBlock,
  Color,
  Details,
  DetailsContent,
  DetailsSummary,
  Document,
  Dropcursor,
  Emoji,
  Figcaption,
  FileHandler,
  Focus,
  FontFamily,
  FontSize,
  Heading,
  Highlight,
  HorizontalRule,
  ImageBlock,
  Indent,
  InvisibleCharacters,
  Link,
  Placeholder,
  Selection,
  SlashCommand,
  StarterKit,
  Subscript,
  Superscript,
  Table,
  TableOfContents,
  TableCell,
  TableHeader,
  TableRow,
  TextAlign,
  TextStyle,
  TrailingNode,
  Typography,
  Underline,
  emojiSuggestion,
  Columns,
  Column,
  TaskItem,
  TaskList,
  UniqueID,
  TypingEffect,
  AITools,
  GhostText,
} from '.'

import { ImageUpload } from './ImageUpload'
import { TableOfContentsNode } from './TableOfContentsNode'
import { isChangeOrigin } from '@tiptap/extension-collaboration'
import { FormatPainter } from './FormatPainter'
import { Comment } from './Comment'
import { AISuggestion } from './AISuggestion'

interface ExtensionKitProps {
  provider?: HocuspocusProvider | null
  getGhostText?: () => { text: string; pos: number } | null
  onClearGhost?: () => void
}

export function ExtensionKit(props: ExtensionKitProps) {
  return [
    TypingEffect.configure({
      typingSpeed: 50,
      charsPerFrame: 1,
    }),
    AITools.configure({
      typingSpeed: 50,
      charsPerFrame: 1,
    }),
    GhostText.configure({
      getGhost: props.getGhostText || (() => null),
      onClearGhost: props.onClearGhost,
    }),
    Document,
    Columns,
    TaskList,
    TaskItem.configure({
      nested: true,
    }),
    Column,
    Selection,
    Heading.configure({
      levels: [1, 2, 3, 4, 5, 6],
    }),
    HorizontalRule,
    UniqueID.configure({
      types: ['paragraph', 'heading', 'blockquote', 'codeBlock', 'table','history'],
      filterTransaction: transaction => !isChangeOrigin(transaction),
    }),
    StarterKit.configure({
      document: false,
      dropcursor: false,
      heading: false,
      horizontalRule: false,
      blockquote: false,
      history: {
        depth: 100,
        newGroupDelay: 1000,
      },
      codeBlock: false,
    }),
    Details.configure({
      persist: true,
      HTMLAttributes: {
        class: 'details',
      },
    }),
    DetailsContent,
    DetailsSummary,
    CodeBlock,
    TextStyle,
    FontSize,
    FontFamily,
    Color,
    TrailingNode,
    Link.configure({
      openOnClick: false,
    }),
    Highlight.configure({ multicolor: true }),
    Underline,
    CharacterCount.configure({ limit: 50000 }),
    TableOfContents,
    TableOfContentsNode,
    ImageUpload.configure({
      clientId: props.provider?.document?.clientID,
    }),
    ImageBlock,
    FileHandler.configure({
      allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
      onDrop: (currentEditor, files, pos) => {
        files.forEach(async file => {
          const url = await API.uploadImage(file)

          currentEditor.chain().setImageBlockAt({ pos, src: url }).focus().run()
        })
      },
      onPaste: (currentEditor, files) => {
        files.forEach(async file => {
          const url = await API.uploadImage(file)

          return currentEditor
            .chain()
            .setImageBlockAt({ pos: currentEditor.state.selection.anchor, src: url })
            .focus()
            .run()
        })
      },
    }),
    Emoji.configure({
      enableEmoticons: true,
      suggestion: emojiSuggestion,
    }),
    TextAlign.extend({
      addKeyboardShortcuts() {
        return {}
      },
    }).configure({
      types: ['heading', 'paragraph'],
    }),
    Subscript,
    Superscript,
    Table,
    TableCell,
    TableHeader,
    TableRow,
    Typography,
    Placeholder.configure({
      includeChildren: true,
      showOnlyCurrent: false,
      placeholder: () => '',
    }),
    SlashCommand,
    Focus,
    Figcaption,
    BlockquoteFigure,
    Dropcursor.configure({
      width: 2,
      class: 'ProseMirror-dropcursor border-black',
    }),
    FormatPainter,
    InvisibleCharacters,
    Comment,
    AISuggestion,
  ]
}

export default ExtensionKit
