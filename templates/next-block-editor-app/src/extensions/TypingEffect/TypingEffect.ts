import { Extension } from '@tiptap/core'
import { applyTypingEffect } from '@/lib/api/typingEffectHelper'

export interface TypingEffectOptions {
  // 打字速度 (ms)
  typingSpeed: number
  // 每次添加字符的数量
  charsPerFrame: number
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    typingEffect: {
      /**
       * 添加带有打字效果的文本
       * @param text 要添加的文本
       * @param onComplete 可选的完成回调
       */
      addTextWithTypingEffect: (
        text: string, 
        onComplete?: () => void
      ) => ReturnType
    }
  }
}

export const TypingEffect = Extension.create<TypingEffectOptions>({
  name: 'typingEffect',

  addOptions() {
    return {
      typingSpeed: 50,
      charsPerFrame: 1,
    }
  },

  addCommands() {
    return {
      addTextWithTypingEffect: (text: string, onComplete?: () => void) => ({ editor }) => {
        if (!text || !editor) {
          return false;
        }

        // 使用辅助函数处理打字效果，并传递完成回调
        applyTypingEffect(
          editor, 
          text, 
          this.options.typingSpeed, 
          this.options.charsPerFrame,
          onComplete
        );
        
        return true;
      },
    };
  },
}); 