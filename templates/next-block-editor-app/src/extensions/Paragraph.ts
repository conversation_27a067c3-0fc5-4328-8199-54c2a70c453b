import { Paragraph as TiptapParagraph } from '@tiptap/extension-paragraph'

export const Paragraph = TiptapParagraph.extend({
  name: 'paragraph',

  // 确保Paragraph扩展能够读取并应用indent属性
  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {
        class: 'paragraph',
      },
    }
  },
  
  addAttributes() {
    return {
      ...this.parent?.(),
      // 显式添加indent属性，确保段落节点能够处理它
      indent: {
        default: 0,
        parseHTML: element => {
          const indentLevel = Number(element.getAttribute("data-indent"))
          return indentLevel || 0
        },
        renderHTML: attributes => {
          if (!attributes.indent || attributes.indent <= 0) {
            return {}
          }
          return { 'data-indent': attributes.indent }
        }
      }
    }
  },
  
  // 添加一些调试信息，以便在渲染时确认属性是否被正确应用
  renderHTML({ node, HTMLAttributes }) {
    // 确保所有属性都被正确传递到HTML元素上
    return ['p', HTMLAttributes, 0]
  },
}) 