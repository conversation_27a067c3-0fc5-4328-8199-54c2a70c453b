import { Extension } from "@tiptap/core"
import { Mark } from '@tiptap/pm/model'

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    formatPainter: {
      /**
       * 启用格式刷功能
       */
      toggleFormatPainter: () => ReturnType
    }
  }
}

// 状态变更时的回调
type StateChangeCallback = (isActive: boolean) => void;

// 记录复制的格式信息
interface CopiedFormat {
  marks: Mark[];
  nodeType: string | null;
  nodeAttrs: Record<string, any> | null;
}

// 存储格式刷状态
const formatPainterState = {
  isActive: false,
  format: {
    marks: [] as Mark[],
    nodeType: null as string | null,
    nodeAttrs: null as Record<string, any> | null
  } as CopiedFormat,
  callbacks: [] as StateChangeCallback[],
  
  // 重置格式刷状态
  reset() {
    this.isActive = false
    this.format = {
      marks: [],
      nodeType: null,
      nodeAttrs: null
    }
    // 调用所有注册的回调
    this.notifyStateChange()
  },

  // 激活格式刷
  activate() {
    this.isActive = true
    // 调用所有注册的回调
    this.notifyStateChange()
  },
  
  // 注册状态变更回调
  onStateChange(callback: StateChangeCallback) {
    this.callbacks.push(callback)
  },
  
  // 移除状态变更回调
  offStateChange(callback: StateChangeCallback) {
    this.callbacks = this.callbacks.filter(cb => cb !== callback)
  },
  
  // 通知所有回调状态变更
  notifyStateChange() {
    this.callbacks.forEach(callback => callback(this.isActive))
  }
}

export const FormatPainter = Extension.create({
  name: "formatPainter",

  addStorage() {
    return {
      onActiveChange: null as StateChangeCallback | null
    }
  },

  addCommands() {
    return {
      toggleFormatPainter: () => ({ editor }) => {
        if (formatPainterState.isActive) {
          // 如果格式刷已激活，重置状态
          formatPainterState.reset()
          return true
        }

        // 获取当前选中文本的范围
        const { state } = editor
        const { selection } = state
        const { empty, from, to } = selection

        // 没有选中内容无法复制格式
        if (empty) {
          return false
        }

        // 清空之前的格式信息
        formatPainterState.format = {
          marks: [],
          nodeType: null,
          nodeAttrs: null
        }
        
        // 标记是否找到了有效的格式
        let foundFormats = false
        
        // 获取选区内第一个节点的类型
        let firstNode = null
        let firstNodePos = 0
        
        state.doc.nodesBetween(from, to, (node, pos) => {
          // 收集第一个非文本节点的类型和属性
          if (firstNode === null && node.type.name !== 'text' && node.type.name !== 'doc') {
            firstNode = node
            firstNodePos = pos
          }
          
          // 收集标记
          if (node.marks && node.marks.length) {
            node.marks.forEach((mark: Mark) => {
              // 避免重复添加相同类型的标记
              const exists = formatPainterState.format.marks.some(
                m => m.type.name === mark.type.name
              )
              
              if (!exists) {
                formatPainterState.format.marks.push(mark)
                foundFormats = true
              }
            })
          }
          
          return true
        })
        
        // 如果找到了非文本节点，记录其类型和属性
        if (firstNode) {
          formatPainterState.format.nodeType = firstNode.type.name
          formatPainterState.format.nodeAttrs = { ...firstNode.attrs }
          foundFormats = true
        }
        
        // 没有复制到任何格式，不激活格式刷
        if (!foundFormats) {
          return false
        }
        
        // 激活格式刷
        formatPainterState.activate()
        
        // 为按钮提供状态变更通知的处理函数
        if (this.storage.onActiveChange) {
          formatPainterState.onStateChange(this.storage.onActiveChange)
        }

        // 创建鼠标松开事件处理函数
        const handleMouseUp = () => {
          setTimeout(() => {
            if (!formatPainterState.isActive) return
            
            const { state, view } = editor
            const { selection } = state
            const { empty, from, to } = selection
            
            // 如果选择为空，不应用格式
            if (empty) return
            
            // 使用命令API应用格式
            let chain = editor.chain().focus()
            
            // 应用所有标记
            if (formatPainterState.format.marks.length > 0) {
              // 先移除选中区域的所有已有标记
              chain = chain.unsetAllMarks()
              
              // 再应用每个复制的标记
              formatPainterState.format.marks.forEach(mark => {
                const markType = mark.type.name
                
                // 根据标记类型调用相应的命令
                switch (markType) {
                  case 'bold':
                    chain = chain.setBold()
                    break
                  case 'italic':
                    chain = chain.setItalic()
                    break
                  case 'underline':
                    chain = chain.setUnderline()
                    break
                  case 'strike':
                    chain = chain.setStrike()
                    break
                  case 'code':
                    chain = chain.setCode()
                    break
                  case 'subscript':
                    chain = chain.setSubscript()
                    break
                  case 'superscript':
                    chain = chain.setSuperscript()
                    break
                  case 'highlight':
                    if (mark.attrs.color) {
                      chain = chain.setHighlight({ color: mark.attrs.color })
                    }
                    break
                  case 'color':
                  case 'textStyle':
                    if (mark.attrs.color) {
                      chain = chain.setColor(mark.attrs.color)
                    }
                    if (mark.attrs.fontSize) {
                      chain = chain.setFontSize(mark.attrs.fontSize)
                    }
                    if (mark.attrs.fontFamily) {
                      chain = chain.setFontFamily(mark.attrs.fontFamily)
                    }
                    break
                  // 可以根据需要添加更多标记类型的处理
                }
              })
            }
            
            // 应用节点类型（如heading）
            if (formatPainterState.format.nodeType) {
              const nodeType = formatPainterState.format.nodeType
              const nodeAttrs = formatPainterState.format.nodeAttrs || {}
              
              // 根据节点类型调用相应的命令
              switch (nodeType) {
                case 'heading':
                  if (nodeAttrs.level) {
                    chain = chain.setHeading({ level: nodeAttrs.level })
                  }
                  break
                case 'paragraph':
                  chain = chain.setParagraph()
                  break
                case 'blockquote':
                  chain = chain.setBlockquote()
                  break
                case 'codeBlock':
                  chain = chain.setCodeBlock()
                  break
                case 'bulletList':
                  chain = chain.toggleBulletList()
                  break
                case 'orderedList':
                  chain = chain.toggleOrderedList()
                  break
                case 'taskList':
                  chain = chain.toggleTaskList()
                  break
                // 可以根据需要添加更多节点类型的处理
              }
              
              // 应用文本对齐方式
              if (nodeAttrs.textAlign) {
                chain = chain.setTextAlign(nodeAttrs.textAlign)
              }
            }
            
            // 执行所有命令
            chain.run()
            
            // 重置格式刷状态
            formatPainterState.reset()
            
            // 移除事件监听
            document.removeEventListener('mouseup', handleMouseUp)
          }, 10)
        }
        
        // 使用document级别的事件，确保捕获所有鼠标松开事件
        document.addEventListener('mouseup', handleMouseUp)
        
        return true
      }
    }
  },
  
  // 销毁扩展时清除状态
  onDestroy() {
    formatPainterState.reset()
  }
})

export default FormatPainter 