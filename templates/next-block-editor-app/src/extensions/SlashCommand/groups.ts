import { Group } from './types'
import { Editor } from '@tiptap/core'
import { XAgentService, ModelConfigManager } from '@/components/AI/services/XAgentService'
import { AIToolType, AI_TOOL_PROMPTS } from '@/extensions/AITools/AITools'
import { applyTypingEffect } from '@/lib/api/typingEffectHelper'

// 默认测试文本
const DEFAULT_TEST_TEXTS = {
  rewrite: "这是一段测试文本，用于演示AI改写功能。通过AI技术，可以用不同的表达方式呈现相同的内容，使文章更加丰富多样。",
  expand: "人工智能正在改变我们的生活和工作方式。现代AI系统可以理解自然语言，分析图像和视频，甚至创作内容。",
  summarize: "人工智能（AI）在过去几十年中取得了巨大进步。从最初的专家系统到现在的深度学习，AI的能力不断提升。现代AI系统可以识别图像、理解自然语言、玩复杂游戏，甚至创作内容。在医疗领域，AI辅助诊断系统帮助医生更准确地检测疾病。在金融行业，AI用于风险评估和欺诈检测。在日常生活中，我们使用语音助手、推荐系统和智能家居设备。尽管AI带来许多好处，但也引发了关于隐私、就业和偏见等问题的讨论。研究人员正致力于开发更透明、公平和可靠的AI系统。"
};

// 使用默认文本进行AI处理，不显示默认文本
const processWithDefaultText = (editor: Editor, toolType: AIToolType) => {
  // 获取默认文本
  const defaultText = DEFAULT_TEST_TEXTS[toolType];
  
  // 创建XAgentService实例
  const service = new XAgentService(null, ModelConfigManager.getCurrentConfig());
  
  // 根据工具类型构建提示词
  const prompt = `${AI_TOOL_PROMPTS[toolType]}\n\n${defaultText}`;
  
  // 发送消息到AI服务处理
  service.sendMessage(prompt, [])
    .then(({ response, error }) => {
      if (error) {
        console.error('AI服务错误:', error);
        return;
      }
      
      // 直接插入处理结果，使用打字效果
      applyTypingEffect(
        editor, 
        response, 
        50, // 打字速度
        1,  // 每次添加的字符数
        () => {
          // 完成回调
        }
      );
    })
    .catch(error => {
      console.error('AI处理异常:', error);
    });
};

export const GROUPS: Group[] = [
  {
    name: 'ai',
    title: 'AI',
    commands: [
      {
        name: 'aiRewrite',
        label: '改写文本',
        iconName: 'PenLine',
        description: '保持原意，使用不同表达方式重写文本',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          // 获取选中文本
          const { from, to } = editor.state.selection
          
          // 如果有选中文本，使用选中的文本
          if (from !== to) {
            // 选中了文本，使用AITools扩展处理
            editor.commands.applyAITool('rewrite');
          } else {
            // 没有选中文本，使用默认文本但不显示
            processWithDefaultText(editor, 'rewrite');
          }
        },
      },
      {
        name: 'aiExpand',
        label: '扩展文本',
        iconName: 'ArrowRightToLine',
        description: '基于选中文本添加更多细节和内容',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          // 获取选中文本
          const { from, to } = editor.state.selection
          
          // 如果有选中文本，使用选中的文本
          if (from !== to) {
            // 选中了文本，使用AITools扩展处理
            editor.commands.applyAITool('expand');
          } else {
            // 没有选中文本，使用默认文本但不显示
            processWithDefaultText(editor, 'expand');
          }
        },
      },
      {
        name: 'aiSummarize',
        label: '总结文本',
        iconName: 'ListChecks',
        description: '提取文本的关键要点并简洁表达',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          // 获取选中文本
          const { from, to } = editor.state.selection
          
          // 如果有选中文本，使用选中的文本
          if (from !== to) {
            // 选中了文本，使用AITools扩展处理
            editor.commands.applyAITool('summarize');
          } else {
            // 没有选中文本，使用默认文本但不显示
            processWithDefaultText(editor, 'summarize');
          }
        },
      },
    ],
  },
  {
    name: 'format',
    title: 'Format',
    commands: [
      {
        name: 'heading1',
        label: 'Heading 1',
        iconName: 'Heading1',
        description: 'High priority section title',
        aliases: ['h1'],
        action: editor => {
          editor.chain().focus().setHeading({ level: 1 }).run()
        },
      },
      {
        name: 'heading2',
        label: 'Heading 2',
        iconName: 'Heading2',
        description: 'Medium priority section title',
        aliases: ['h2'],
        action: editor => {
          editor.chain().focus().setHeading({ level: 2 }).run()
        },
      },
      {
        name: 'heading3',
        label: 'Heading 3',
        iconName: 'Heading3',
        description: 'Low priority section title',
        aliases: ['h3'],
        action: editor => {
          editor.chain().focus().setHeading({ level: 3 }).run()
        },
      },
      {
        name: 'bulletList',
        label: 'Bullet List',
        iconName: 'List',
        description: 'Unordered list of items',
        aliases: ['ul'],
        action: editor => {
          editor.chain().focus().toggleBulletList().run()
        },
      },
      {
        name: 'numberedList',
        label: 'Numbered List',
        iconName: 'ListOrdered',
        description: 'Ordered list of items',
        aliases: ['ol'],
        action: editor => {
          editor.chain().focus().toggleOrderedList().run()
        },
      },
      {
        name: 'taskList',
        label: 'Task List',
        iconName: 'ListTodo',
        description: 'Task list with todo items',
        aliases: ['todo'],
        action: editor => {
          editor.chain().focus().toggleTaskList().run()
        },
      },
      {
        name: 'toggleList',
        label: 'Toggle List',
        iconName: 'ListCollapse',
        description: 'Toggles can show and hide content',
        aliases: ['toggle'],
        action: editor => {
          editor.chain().focus().setDetails().run()
        },
      },
      {
        name: 'blockquote',
        label: 'Blockquote',
        iconName: 'Quote',
        description: 'Element for quoting',
        action: editor => {
          editor.chain().focus().setBlockquote().run()
        },
      },
      {
        name: 'codeBlock',
        label: 'Code Block',
        iconName: 'SquareCode',
        description: 'Code block with syntax highlighting',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          editor.chain().focus().setCodeBlock().run()
        },
      },
    ],
  },
  {
    name: 'insert',
    title: 'Insert',
    commands: [
      {
        name: 'table',
        label: 'Table',
        iconName: 'Table',
        description: 'Insert a table',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: false }).run()
        },
      },
      {
        name: 'image',
        label: 'Image',
        iconName: 'Image',
        description: 'Insert an image',
        aliases: ['img'],
        action: editor => {
          editor.chain().focus().setImageUpload().run()
        },
      },
      {
        name: 'columns',
        label: 'Columns',
        iconName: 'Columns2',
        description: 'Add two column content',
        aliases: ['cols'],
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          editor
            .chain()
            .focus()
            .setColumns()
            .focus(editor.state.selection.head - 1)
            .run()
        },
      },
      {
        name: 'horizontalRule',
        label: 'Horizontal Rule',
        iconName: 'Minus',
        description: 'Insert a horizontal divider',
        aliases: ['hr'],
        action: editor => {
          editor.chain().focus().setHorizontalRule().run()
        },
      },
      {
        name: 'toc',
        label: 'Table of Contents',
        iconName: 'Book',
        aliases: ['outline'],
        description: 'Insert a table of contents',
        shouldBeHidden: editor => editor.isActive('columns'),
        action: editor => {
          editor.chain().focus().insertTableOfContents().run()
        },
      },
    ],
  },
]

export default GROUPS
