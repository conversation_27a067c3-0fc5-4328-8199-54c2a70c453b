import React from 'react'
import { SuggestionRule } from './ruleConfigStore'

interface SuggestionPopupProps {
  suggestion: string
  onApply: () => void
  onReject: () => void
  rule: SuggestionRule
}

const SuggestionPopup: React.FC<SuggestionPopupProps> = ({ suggestion, onApply, onReject, rule }) => {
  return (
    <div
      className="rounded-lg shadow-xl border bg-white dark:bg-neutral-900 p-4 min-w-[220px] max-w-xs z-50"
      style={{ borderColor: rule.color, borderWidth: 2 }}
    >
      <div className="mb-2 text-sm text-gray-700 dark:text-gray-200">
        <span className="font-semibold" style={{ color: rule.color }}>{rule.title}</span>
      </div>
      <div className="mb-3 text-sm text-gray-900 dark:text-gray-100">{suggestion}</div>
      <div className="flex gap-2">
        <button
          className="flex-1 bg-blue-500 hover:bg-blue-600 text-white rounded py-1 text-xs font-medium"
          onClick={onApply}
        >接受</button>
        <button
          className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded py-1 text-xs font-medium"
          onClick={onReject}
        >拒绝</button>
      </div>
    </div>
  )
}

export default SuggestionPopup
