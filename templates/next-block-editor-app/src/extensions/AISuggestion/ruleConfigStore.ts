import { create } from 'zustand'

export type SuggestionRule = {
  id: string
  title: string
  prompt: string
  color: string
}

interface RuleConfigState {
  rules: SuggestionRule[]
  addRule: (rule: SuggestionRule) => void
  updateRule: (id: string, rule: Partial<SuggestionRule>) => void
  deleteRule: (id: string) => void
}

export const useRuleConfigStore = create<RuleConfigState>((set) => ({
  rules: [
    {
      id: 'spell-check',
      title: 'Spell-Check English Text',
      prompt: 'Review the provided English text and identify any spelling errors',
      color: '#fbbf24',
    },
    {
      id: 'translate',
      title: 'Translate Non-English Text',
      prompt: 'For any text provided in a language other than English, translate it into English',
      color: '#60a5fa',
    },
  ],
  addRule: (rule) => set((state) => ({ rules: [...state.rules, rule] })),
  updateRule: (id, rule) => set((state) => ({
    rules: state.rules.map((r) => (r.id === id ? { ...r, ...rule } : r)),
  })),
  deleteRule: (id) => set((state) => ({
    rules: state.rules.filter((r) => r.id !== id),
  })),
}))
