import React, { useState } from 'react'
import { useRuleConfigStore, SuggestionRule } from './ruleConfigStore'
import { nanoid } from 'nanoid'

const colorOptions = [
  '#fbbf24', '#60a5fa', '#34d399', '#f472b6', '#a78bfa', '#f87171', '#facc15', '#818cf8', '#6ee7b7', '#fcd34d', '#c084fc', '#fca5a5', '#f9fafb', '#d1d5db', '#a3e635', '#f3f4f6',
]

export const RuleConfigPanel: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { rules, addRule, updateRule, deleteRule } = useRuleConfigStore()
  const [formTitle, setFormTitle] = useState('')
  const [formPrompt, setFormPrompt] = useState('')
  const [formColor, setFormColor] = useState(colorOptions[0])
  const [editingId, setEditingId] = useState<string | null>(null)

  const handleEdit = (rule: SuggestionRule) => {
    setEditingId(rule.id)
    setFormTitle(rule.title)
    setFormPrompt(rule.prompt)
    setFormColor(rule.color)
  }

  const handleSave = () => {
    if (!formTitle.trim() || !formPrompt.trim()) return
    if (editingId) {
      updateRule(editingId, { title: formTitle, prompt: formPrompt, color: formColor })
    } else {
      addRule({ id: nanoid(), title: formTitle, prompt: formPrompt, color: formColor })
    }
    setEditingId(null)
    setFormTitle('')
    setFormPrompt('')
    setFormColor(colorOptions[0])
  }

  const handleCancel = () => {
    setEditingId(null)
    setFormTitle('')
    setFormPrompt('')
    setFormColor(colorOptions[0])
  }

  return (
    <div className="fixed top-20 right-10 z-50 w-96 bg-white dark:bg-neutral-900 rounded-lg shadow-lg border border-gray-200 dark:border-neutral-700 p-6">
      <div className="flex justify-between items-center mb-4">
        <div className="text-lg font-bold">建议样式规则配置</div>
        <button className="text-gray-400 hover:text-gray-700" onClick={onClose} aria-label="关闭">✕</button>
      </div>
      <div className="space-y-4 max-h-80 overflow-y-auto">
        {rules.map(rule => (
          <div key={rule.id} className="bg-gray-50 dark:bg-neutral-800 rounded p-3 flex flex-col gap-1 border-l-4" style={{ borderColor: rule.color }}>
            <div className="flex justify-between items-center">
              <span className="font-medium text-sm" style={{ color: rule.color }}>{rule.title}</span>
              <div className="flex gap-2">
                <button className="text-xs text-blue-400 hover:text-blue-600" onClick={() => handleEdit(rule)}>修改</button>
                <button className="text-xs text-red-400 hover:text-red-600" onClick={() => deleteRule(rule.id)}>删除</button>
              </div>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-300">{rule.prompt}</div>
          </div>
        ))}
      </div>
      <div className="mt-6 border-t pt-4">
        <div className="font-semibold text-sm mb-2">{editingId ? '编辑规则' : '新建规则'}</div>
        <input
          className="w-full mb-2 px-2 py-1 border rounded text-sm bg-gray-100 dark:bg-neutral-800"
          placeholder="规则标题"
          value={formTitle}
          onChange={e => setFormTitle(e.target.value)}
        />
        <textarea
          className="w-full mb-2 px-2 py-1 border rounded text-sm bg-gray-100 dark:bg-neutral-800"
          placeholder="规则描述/Prompt"
          value={formPrompt}
          onChange={e => setFormPrompt(e.target.value)}
        />
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs">颜色：</span>
          {colorOptions.map(c => (
            <button
              key={c}
              className={`w-5 h-5 rounded-full border-2 ${formColor === c ? 'border-black dark:border-white' : 'border-transparent'}`}
              style={{ background: c }}
              onClick={() => setFormColor(c)}
              aria-label={`选择颜色${c}`}
            />
          ))}
        </div>
        <div className="flex gap-2">
          <button
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white rounded py-1 text-sm font-medium mt-1"
            onClick={handleSave}
          >{editingId ? '保存规则' : '添加规则'}</button>
          {editingId && (
            <button
              className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded py-1 text-sm font-medium mt-1"
              onClick={handleCancel}
            >取消</button>
          )}
        </div>
      </div>
    </div>
  )
}
