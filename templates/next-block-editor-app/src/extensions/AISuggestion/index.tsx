import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { ReactRenderer } from '@tiptap/react'
import tippy, { Instance } from 'tippy.js'
import SuggestionPopup from './SuggestionPopup'
import { useRuleConfigStore } from './ruleConfigStore'
import { Decoration, DecorationSet } from 'prosemirror-view'

const pluginKey = new PluginKey('aiSuggestion')

// 模拟建议数据
const mockSuggestions = [
  {
    from: 18,
    to: 28,
    text: 'aplication',
    suggestion: 'application',
    ruleId: 'spell-check',
  },
  {
    from: 70,
    to: 80,
    text: 'Hola',
    suggestion: 'Hello',
    ruleId: 'translate',
  },
]

export const AISuggestion = Extension.create({
  name: 'aiSuggestion',

  addOptions() {
    return {
      enabled: true, // 通过 options 控制开关
    }
  },

  addProseMirrorPlugins() {
    const editor = this.editor
    return [
      new Plugin({
        key: pluginKey,
        state: {
          init: () => ({ suggestions: mockSuggestions }),
          apply(tr, value) {
            // TODO: 后续可根据 tr/meta 更新建议
            return value
          },
        },
        props: {
          decorations: (state) => {
            if (!this.options.enabled) return null
            const doc = state.doc
            const decorations = mockSuggestions.map(sug =>
              Decoration.inline(sug.from, sug.to, {
                class: 'bg-yellow-100 underline decoration-dotted cursor-pointer',
                'data-suggestion-id': sug.from + '-' + sug.to,
              })
            )
            return DecorationSet.create(doc, decorations)
          },
          handleClick: (view, pos, event) => {
            if (!this.options.enabled) return false
            const target = event.target as HTMLElement
            const sugId = target.getAttribute('data-suggestion-id')
            if (!sugId) return false
            const sug = mockSuggestions.find(s => sugId === s.from + '-' + s.to)
            if (!sug) return false
            // 弹出建议弹窗
            const rule = useRuleConfigStore.getState().rules.find(r => r.id === sug.ruleId) || { title: '建议', color: '#fbbf24', prompt: '' }
            const popup = tippy(target, {
              content: '',
              trigger: 'manual',
              placement: 'bottom-start',
              interactive: true,
              appendTo: () => document.body,
              getReferenceClientRect: () => {
                const start = view.coordsAtPos(sug.from)
                const end = view.coordsAtPos(sug.to)
                return {
                  top: start.top,
                  left: start.left,
                  right: end.right,
                  bottom: start.bottom,
                  width: end.right - start.left,
                  height: start.bottom - start.top,
                } as DOMRect
              },
              popperOptions: {
                modifiers: [
                  { name: 'flip', enabled: true },
                  { name: 'preventOverflow', enabled: true },
                ],
              },
              onShow(instance) {
                instance.setContent('')
                const react = new ReactRenderer(
                  SuggestionPopup,
                  {
                    editor,
                    props: {
                      suggestion: `${rule.title}: ${sug.suggestion}`,
                      rule,
                      onApply: () => {
                        // 替换文字
                        view.dispatch(
                          view.state.tr.insertText(sug.suggestion, sug.from, sug.to)
                        )
                        // 移除高亮样式和建议项
                        mockSuggestions.splice(mockSuggestions.indexOf(sug), 1)
                        view.dispatch(view.state.tr) // 触发装饰器刷新
                        instance.hide()
                      },
                      onReject: () => {
                        // 只移除高亮样式和建议项
                        mockSuggestions.splice(mockSuggestions.indexOf(sug), 1)
                        view.dispatch(view.state.tr) // 触发装饰器刷新
                        instance.hide()
                      },
                    },
                  }
                )
                instance.popper.appendChild(react.element)
              },
              onHidden(instance) {
                instance.destroy()
              },
            })
            popup.show()
            return true
          },
        },
      }),
    ]
  },
})
