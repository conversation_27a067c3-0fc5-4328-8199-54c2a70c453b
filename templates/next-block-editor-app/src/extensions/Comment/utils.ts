import { Editor } from '@tiptap/react';
import { CommentPosition } from './types';

/**
 * 计算批注位置
 * @param editor 编辑器实例
 * @param pos 批注位置
 * @returns 计算得到的批注位置
 */
export const getCommentPosition = (editor: Editor, pos: number): CommentPosition | null => {
  try {
    if (!editor || !editor.view || pos === undefined || pos < 0) return null;

    // 获取DOM位置信息
    const { view } = editor;
    const domPos = view.coordsAtPos(pos);
    if (!domPos) return null;

    // 获取编辑器的边界位置
    const editorBounds = editor.view.dom.getBoundingClientRect();
    
    // 计算相对于编辑器的位置
    const left = domPos.left - editorBounds.left;
    const top = domPos.top - editorBounds.top;
    const height = domPos.bottom - domPos.top;

    return { left, top, height };
  } catch (error) {
    console.error('批注位置计算错误:', error);
    return null;
  }
}; 