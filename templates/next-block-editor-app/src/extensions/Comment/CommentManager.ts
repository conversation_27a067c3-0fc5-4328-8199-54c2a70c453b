import { Editor } from '@tiptap/react';
import { Comment, CommentState, SuggestionItem } from './types';
import { nanoid } from 'nanoid';
import { getCommentPosition } from './utils';

export class CommentManager {
  private comments: Comment[] = [];
  private editor: Editor;
  private listeners: Array<() => void> = [];

  constructor(editor: Editor) {
    this.editor = editor;
  }

  /**
   * 获取所有批注
   */
  public getComments(): Comment[] {
    return [...this.comments];
  }

  /**
   * 添加批注
   */
  public addComment(from: number, to: number, content: string): Comment {
    const original = this.editor.state.doc.textBetween(
      from, 
      to, 
      '\n'
    );

    const newComment: Comment = {
      id: nanoid(),
      anchor: { from, to },
      original,
      content,
      suggestions: [],
      state: CommentState.active,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.comments.push(newComment);
    this.notifyListeners();
    return newComment;
  }

  /**
   * 更新批注
   */
  public updateComment(id: string, data: Partial<Comment>): Comment | null {
    const index = this.comments.findIndex(comment => comment.id === id);
    if (index === -1) return null;

    const updatedComment = {
      ...this.comments[index],
      ...data,
      updatedAt: new Date().toISOString()
    };

    this.comments[index] = updatedComment;
    this.notifyListeners();
    return updatedComment;
  }

  /**
   * 删除批注
   */
  public deleteComment(id: string): boolean {
    const index = this.comments.findIndex(comment => comment.id === id);
    if (index === -1) return false;

    this.comments.splice(index, 1);
    this.notifyListeners();
    return true;
  }

  /**
   * 获取批注
   */
  public getComment(id: string): Comment | undefined {
    return this.comments.find(comment => comment.id === id);
  }

  /**
   * 添加批注建议
   */
  public addSuggestion(commentId: string, text: string): SuggestionItem | null {
    const comment = this.getComment(commentId);
    if (!comment) return null;

    const suggestion: SuggestionItem = {
      id: nanoid(),
      text,
      createdAt: new Date().toISOString()
    };

    return this.updateComment(commentId, {
      suggestions: [...comment.suggestions, suggestion]
    })?.suggestions.slice(-1)[0] || null;
  }

  /**
   * 解决批注
   */
  public resolveComment(id: string): Comment | null {
    return this.updateComment(id, { state: CommentState.resolved });
  }

  /**
   * 隐藏批注
   */
  public hideComment(id: string): Comment | null {
    return this.updateComment(id, { state: CommentState.hidden });
  }

  /**
   * 激活批注
   */
  public activateComment(id: string): Comment | null {
    return this.updateComment(id, { state: CommentState.active });
  }

  /**
   * 获取批注位置
   */
  public getCommentPosition(commentId: string) {
    const comment = this.getComment(commentId);
    if (!comment) return null;
    
    return getCommentPosition(this.editor, comment.anchor.from);
  }

  /**
   * 添加变更监听器
   */
  public addChangeListener(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => this.removeChangeListener(listener);
  }

  /**
   * 移除变更监听器
   */
  public removeChangeListener(listener: () => void): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }
} 