import { Editor } from '@tiptap/react';

// 批注状态枚举
export enum CommentState {
  active = 'active',
  hidden = 'hidden',
  resolved = 'resolved'
}

// 建议项类型
export type SuggestionItem = {
  id: string;
  text: string;
  createdAt: string;
  accepted?: boolean; // 标记是否已接受该修订建议
};

// 批注类型
export type Comment = {
  id: string;
  anchor: { from: number; to: number };
  original: string;
  content?: string;  // 批注内容
  suggestions: SuggestionItem[];
  state: CommentState;
  createdAt: string;
  updatedAt: string;
};

// 批注定位辅助类型
export type CommentPosition = {
  left: number;
  top: number;
  height: number;
};

// 计算批注位置的工具函数类型
export type GetCommentPositionFn = (editor: Editor, pos: number) => CommentPosition | null; 