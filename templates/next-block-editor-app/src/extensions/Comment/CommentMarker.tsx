import React, { useCallback, useEffect, useState, useRef } from 'react'
import { Editor, NodeViewRendererProps } from '@tiptap/react'
import { useCommentStore, SuggestionItem } from '@/lib/stores/commentStore'
import { CommentItem } from '@/components/panels/CommentPanel/CommentItem'
import { Comment } from './types'
import { getCommentPosition } from './getCommentPosition'

// 批注标记组件的属性
interface CommentMarkerProps {
  editor: Editor
  getPos: () => number
  node: any
  popupComponent: any
  onUpdateComment?: (comment: Comment) => void
}

// 简化版批注标记组件，用于在编辑器中直接使用
export const CommentMarker: React.FC<{ editor: Editor }> = ({ editor }) => {
  const { 
    comments, 
    activeCommentId, 
    setActiveComment, 
    deleteComment, 
    setCommentState,
    addSuggestion,
    updateComment 
  } = useCommentStore()

  const [newSuggestion, setNewSuggestion] = useState<string>('')
  const [visiblePopupId, setVisiblePopupId] = useState<string | null>(null)
  const markersRef = useRef<{[key: string]: HTMLElement}>({})
  const connectorsRef = useRef<{[key: string]: HTMLElement}>({})
  const popupsRef = useRef<{[key: string]: HTMLElement}>({})
  const containerRef = useRef<HTMLDivElement | null>(null)
  
  // 创建DOM容器
  useEffect(() => {
    // 创建一个专门的容器来放置所有批注相关元素
    const container = document.createElement('div')
    container.className = 'comment-markers-container'
    container.style.position = 'absolute'
    container.style.top = '0'
    container.style.left = '0'
    container.style.width = '100%'
    container.style.height = '100%'
    container.style.pointerEvents = 'none'
    container.style.zIndex = '90' // 设置z-index小于工具栏(100)，但高于普通内容
    
    // 找到编辑器容器并插入我们的容器为其第一个子元素
    const editorContainer = document.querySelector('.ProseMirror')?.parentElement
    if (editorContainer) {
      // 将容器设置为相对定位以便内部元素可以正确定位
      if(window.getComputedStyle(editorContainer).position === 'static') {
        editorContainer.style.position = 'relative'
      }
      
      if (editorContainer.firstChild) {
        editorContainer.insertBefore(container, editorContainer.firstChild)
      } else {
        editorContainer.appendChild(container)
      }
      containerRef.current = container
    } else {
      document.body.appendChild(container)
      containerRef.current = container
    }
    
    return () => {
      // 清理容器
      container.remove()
    }
  }, [])
  
  // 视口坐标转换为容器相对坐标
  const viewportToContainerCoords = useCallback((x: number, y: number) => {
    if (!containerRef.current) return { x, y }
    
    const containerRect = containerRef.current.getBoundingClientRect()
    return {
      x: x - containerRect.left,
      y: y - containerRect.top
    }
  }, [])
  
  // 获取当前视图中的所有批注
  const getVisibleComments = useCallback(() => {
    if (!editor || !editor.view || !editor.state) return []
    
    // 获取所有带有data-comment-id的元素
    const commentElements = document.querySelectorAll('[data-comment-id]')
    const visibleComments: { id: string; element: Element; index: number }[] = []
    
    // 获取批注数组，基于其索引位置设置序号
    const { comments } = useCommentStore.getState()
    
    commentElements.forEach((element) => {
      const commentId = element.getAttribute('data-comment-id')
      if (commentId) {
        // 确保批注在我们的存储中
        const comment = comments.find(c => c.id === commentId)
        if (comment) {
          // 查找评论在当前评论数组中的索引位置
          const commentIndex = comments.findIndex(c => c.id === commentId)
          visibleComments.push({ 
            id: commentId, 
            element, 
            // 从1开始，保持与CommentPanel一致的编号
            index: commentIndex + 1 
          })
        }
      }
    })
    
    return visibleComments
  }, [editor, comments])
  
  // 处理点击批注标记的事件
  const handleMarkerClick = useCallback((commentId: string, event: MouseEvent) => {
    // 阻止事件冒泡，这样父级的pointerEvents: none不会阻止点击
    event.stopPropagation()
    
    if (visiblePopupId === commentId) {
      setVisiblePopupId(null)
    } else {
      setVisiblePopupId(commentId)
      setActiveComment(commentId)
    }
  }, [setActiveComment, visiblePopupId])
  
  // 创建一个批注标记
  const createMarker = useCallback((id: string, index: number) => {
    // 如果已经存在就返回，或者容器不存在就退出
    if (markersRef.current[id] || !containerRef.current) return markersRef.current[id]
    
    const marker = document.createElement('div')
    marker.className = 'comment-marker'
    marker.dataset.commentId = id
    marker.style.position = 'absolute' // 使用absolute相对于容器定位
    marker.style.width = '14px'
    marker.style.height = '14px'
    marker.style.borderRadius = '50%'
    marker.style.backgroundColor = '#D32F2F'
    marker.style.color = '#fff'
    marker.style.fontSize = '9px'
    marker.style.fontFamily = 'Arial'
    marker.style.display = 'flex'
    marker.style.justifyContent = 'center'
    marker.style.alignItems = 'center'
    marker.style.cursor = 'pointer'
    marker.style.userSelect = 'none'
    marker.style.pointerEvents = 'auto' // 允许接收点击事件
    marker.style.zIndex = '91' // 确保标记在连接线之上
    marker.innerText = `${index}`
    
    // 添加点击事件处理
    marker.addEventListener('click', (e) => handleMarkerClick(id, e as MouseEvent))
    
    // 添加到容器
    containerRef.current.appendChild(marker)
    markersRef.current[id] = marker
    
    return marker
  }, [handleMarkerClick])
  
  // 创建批注气泡
  const createCommentBubble = useCallback((id: string, comment: Comment, index: number) => {
    // 如果已经存在就返回，或者容器不存在就退出
    if (popupsRef.current[id] || !containerRef.current) return popupsRef.current[id]
    
    const bubble = document.createElement('div')
    bubble.className = 'comment-bubble'
    bubble.dataset.commentId = id
    bubble.style.position = 'absolute' // 使用absolute相对于容器定位
    bubble.style.width = '250px'
    bubble.style.pointerEvents = 'auto' // 允许接收点击事件
    bubble.style.zIndex = '90' // 批注气泡层级
    bubble.style.transform = 'translateY(-50%)' // 垂直居中
    
    // 渲染批注气泡内容
    const content = document.createElement('div')
    content.className = 'border rounded-md overflow-hidden shadow-sm bg-white mb-4'
    
    // 创建批注头部
    const header = document.createElement('div')
    header.className = 'bg-neutral-50 p-2 flex justify-between items-center border-b border-gray-200 cursor-pointer'
    header.innerHTML = `
      <div class="flex items-center gap-2">
        <div class="h-5 w-5 rounded-full bg-red-600 flex items-center justify-center text-xs text-white font-semibold">${index}</div>
        <span class="text-sm font-medium">批注</span>
      </div>
      <div class="flex items-center gap-1">
        <button class="text-gray-500 hover:text-gray-700 edit-btn" aria-label="编辑"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></button>
        <button class="text-gray-500 hover:text-gray-700 delete-btn" aria-label="删除"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg></button>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
      </div>
    `
    
    // 创建批注内容区域
    const commentBody = document.createElement('div')
    commentBody.className = 'p-2 bg-white'
    
    // 创建原文预览
    const originalText = document.createElement('div')
    originalText.className = 'p-2 bg-[#FFF3CD] rounded text-sm mb-2 font-仿宋体 comment-original'
    originalText.innerText = comment.original
    originalText.title = '点击定位到原文'
    
    // 创建批注内容输入区域
    const commentContent = document.createElement('div')
    const hasContent = comment.content && comment.content.trim().length > 0
    
    if (hasContent) {
      commentContent.className = 'text-sm text-gray-700 p-2 font-微软雅黑 comment-content'
      // 为主要内容添加额外的class以便稍后选择
      commentContent.innerHTML = `
        <div class="comment-main-content">${comment.content || ''}</div>
        ${comment.updatedAt ? `<div class="text-xs text-gray-400 mt-2 text-right">更新于：${new Date(comment.updatedAt).toLocaleString()}</div>` : ''}
      `
    } else {
      // 如果没有内容，直接显示输入框
      commentContent.className = 'text-sm text-gray-700 p-2 font-微软雅黑 comment-content'
      
      const textarea = document.createElement('textarea')
      textarea.className = "w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-微软雅黑 resize-none comment-textarea"
      textarea.placeholder = "请输入批注内容（必填）..."
      textarea.rows = 3
      textarea.value = comment.content || ''
      textarea.required = true
      
      // 创建按钮容器
      const buttonContainer = document.createElement('div')
      buttonContainer.className = "flex justify-end mt-2"
      
      const saveButton = document.createElement('button')
      saveButton.className = "px-2 py-1 text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 rounded"
      saveButton.innerText = "保存批注"
      saveButton.disabled = true
      
      textarea.addEventListener('input', () => {
        // 禁用/启用保存按钮
        saveButton.disabled = textarea.value.trim().length === 0
        
        // 根据内容自动调整高度
        textarea.style.height = 'auto'
        textarea.style.height = textarea.scrollHeight + 'px'
      })
      
      saveButton.addEventListener('click', () => {
        const content = textarea.value.trim()
        if (content) {
          const now = new Date().toISOString()
          
          // 更新批注存储 - 使用最新的store实例
          const currentCommentStore = useCommentStore.getState();
          currentCommentStore.updateComment(id, {
            content: content, 
            updatedAt: now 
          })
          
          // 更新显示内容
          commentContent.innerHTML = `
            <div class="comment-main-content">${content}</div>
            <div class="text-xs text-gray-400 mt-2 text-right">更新于：${new Date(now).toLocaleString()}</div>
          `
          
          // 移除编辑组件
          textarea.remove()
          buttonContainer.remove()
        }
      })
      
      buttonContainer.appendChild(saveButton)
      commentContent.appendChild(textarea)
      commentContent.appendChild(buttonContainer)
      
      // 设置文本区域的初始高度，以便容纳所有内容
      setTimeout(() => {
        // 根据内容调整高度
        textarea.style.height = 'auto'
        textarea.style.height = textarea.scrollHeight + 'px'
      }, 0)
    }
    
    // 添加修订内容区域
    const revisionSection = document.createElement('div')
    revisionSection.className = 'border-t border-gray-200 mt-1'
    
    // 修订标题栏（可折叠）
    const revisionHeader = document.createElement('div')
    revisionHeader.className = 'p-2 bg-gray-50 flex justify-between items-center cursor-pointer'
    revisionHeader.innerHTML = `
      <span class="text-sm font-medium">修订内容</span>
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
    `
    
    // 创建修订内容
    const revisionContent = document.createElement('div')
    revisionContent.className = 'p-2 bg-white hidden'
    
    // 检查是否有接受过的修订建议
    let hasAcceptedRevision = false
    if (comment.suggestions && comment.suggestions.length > 0) {
      // 查找最后一个被接受的修订建议
      for (let i = comment.suggestions.length - 1; i >= 0; i--) {
        const suggestion = comment.suggestions[i]
        if (suggestion.accepted) {
          hasAcceptedRevision = true
          
          // 显示已接受的修订建议
          const acceptedRevision = document.createElement('div')
          acceptedRevision.className = 'bg-green-50 p-3 rounded-md text-sm mb-2 border border-green-200'
          acceptedRevision.innerHTML = `
            <div class="flex items-center mb-1 text-green-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
              <span class="text-xs font-semibold">已接受修订建议</span>
            </div>
            <div class="text-sm">${suggestion.text}</div>
          `
          revisionContent.appendChild(acceptedRevision)
          break
        }
      }
    }
    
    // 创建修订建议编辑区域
    const revisionEditContainer = document.createElement('div')
    revisionEditContainer.className = 'space-y-2'
    
    // 创建修订建议输入框和按钮区域
    const textarea = document.createElement('textarea')
    textarea.className = "w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-微软雅黑 resize-none"
    textarea.placeholder = "输入修订建议..."
    textarea.rows = 3
    
    // 获取修订建议内容
    let currentSuggestionText = ''
    let isInEditMode = true
    
    // 最后一个非接受状态的建议
    if (comment.suggestions && comment.suggestions.length > 0) {
      for (let i = comment.suggestions.length - 1; i >= 0; i--) {
        const suggestion = comment.suggestions[i]
        if (!suggestion.accepted) {
          currentSuggestionText = suggestion.text
          break
        }
      }
    }
    
    textarea.value = currentSuggestionText
    
    // 按钮容器
    const buttonContainer = document.createElement('div')
    buttonContainer.className = 'flex justify-between'
    
    // 保存/编辑按钮
    const editSaveButton = document.createElement('button')
    editSaveButton.className = "px-2 py-1 text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 rounded"
    editSaveButton.innerText = "保存"
    
    // 应用按钮
    const applyButton = document.createElement('button')
    applyButton.className = "px-2 py-1 text-xs bg-green-50 text-green-600 hover:bg-green-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
    applyButton.innerText = "应用"
    applyButton.disabled = true // 初始状态下禁用
    
    // 处理保存/编辑按钮点击
    editSaveButton.addEventListener('click', () => {
      if (isInEditMode) {
        // 保存操作
        if (textarea.value.trim()) {
          // 保存修订建议
          const suggestionText = textarea.value.trim()
          const now = new Date().toISOString()
          addSuggestion(id, suggestionText, false)
          currentSuggestionText = suggestionText
          
          // 切换到查看模式
          isInEditMode = false
          textarea.disabled = true
          editSaveButton.innerText = "编辑"
          applyButton.disabled = false // 启用应用按钮
          
          // 显示时间
          const timeDisplay = document.createElement('div')
          timeDisplay.className = 'text-xs text-gray-400 mt-1 text-right'
          timeDisplay.innerText = `保存于：${new Date(now).toLocaleString()}`
          
          // 如果已经有时间显示，就替换它
          const existingTime = revisionEditContainer.querySelector('.text-xs.text-gray-400')
          if (existingTime) {
            existingTime.remove()
          }
          
          revisionEditContainer.appendChild(timeDisplay)
        }
      } else {
        // 编辑操作
        isInEditMode = true
        textarea.disabled = false
        editSaveButton.innerText = "保存"
        applyButton.disabled = true // 禁用应用按钮
        textarea.focus()
      }
    })
    
    // 应用修订
    applyButton.addEventListener('click', () => {
      if (!isInEditMode && currentSuggestionText.trim()) {
        // 应用修订建议到原文
        if (editor) {
          editor.commands.command(({ tr, dispatch }) => {
            if (dispatch && currentSuggestionText.trim()) {
              tr.replaceWith(comment.anchor.from, comment.anchor.to, editor.schema.text(currentSuggestionText))
              
              // 重新应用批注标记，确保新文本也有高亮和下划线样式
              const commentMark = editor.schema.marks.comment.create({ id })
              tr.addMark(comment.anchor.from, comment.anchor.from + currentSuggestionText.length, commentMark)
              
              dispatch(tr)
            }
            return true
          })
          
          // 更新批注中的原始文本
          updateComment(id, {
            original: currentSuggestionText,
            // 标记最新的修订建议为已接受
            suggestions: [...(comment.suggestions || []), { 
              id: Date.now().toString(), 
              text: currentSuggestionText, 
              createdAt: new Date().toISOString(),
              accepted: true 
            }]
          })
          
          // 更新原文显示
          originalText.innerText = currentSuggestionText
          
          // 清空当前修订区域并显示已接受状态
          revisionEditContainer.innerHTML = ''
          
          // 显示已接受的修订建议
          const acceptedRevision = document.createElement('div')
          acceptedRevision.className = 'bg-green-50 p-3 rounded-md text-sm mb-2 border border-green-200'
          acceptedRevision.innerHTML = `
            <div class="flex items-center mb-1 text-green-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
              <span class="text-xs font-semibold">已接受修订建议</span>
            </div>
            <div class="text-sm">${currentSuggestionText}</div>
          `
          revisionContent.appendChild(acceptedRevision)
          
          // 隐藏编辑区域
          revisionEditContainer.style.display = 'none'
          hasAcceptedRevision = true
        }
      }
    })
    
    // 文本输入事件
    textarea.addEventListener('input', () => {
      if (isInEditMode) {
        // 编辑模式下，保存按钮状态取决于文本框是否有内容
        editSaveButton.disabled = textarea.value.trim().length === 0
      }
    })
    
    // 根据初始状态设置
    if (currentSuggestionText) {
      // 如果有已保存的建议但未应用，设置为查看模式
      isInEditMode = false
      textarea.disabled = true
      editSaveButton.innerText = "编辑"
      applyButton.disabled = false
    } else {
      // 初始状态为编辑模式
      isInEditMode = true
      textarea.disabled = false
      editSaveButton.innerText = "保存"
      applyButton.disabled = true
    }
    
    // 添加元素到容器
    buttonContainer.appendChild(editSaveButton)
    buttonContainer.appendChild(applyButton)
    revisionEditContainer.appendChild(textarea)
    revisionEditContainer.appendChild(buttonContainer)
    
    // 显示上次保存时间（如果有）
    if (currentSuggestionText && comment.suggestions && comment.suggestions.length > 0) {
      // 查找最后一个非接受状态的建议的时间
      for (let i = comment.suggestions.length - 1; i >= 0; i--) {
        const suggestion = comment.suggestions[i]
        if (!suggestion.accepted && suggestion.createdAt) {
          const timeDisplay = document.createElement('div')
          timeDisplay.className = 'text-xs text-gray-400 mt-1 text-right'
          timeDisplay.innerText = `保存于：${new Date(suggestion.createdAt).toLocaleString()}`
          revisionEditContainer.appendChild(timeDisplay)
          break
        }
      }
    }
    
    // 如果已经有接受的修订建议，隐藏编辑区域
    if (hasAcceptedRevision) {
      revisionEditContainer.style.display = 'none'
    }
    
    revisionContent.appendChild(revisionEditContainer)
    
    // 组装修订部分
    revisionSection.appendChild(revisionHeader)
    revisionSection.appendChild(revisionContent)
    
    // 添加折叠事件
    revisionHeader.addEventListener('click', () => {
      if (revisionContent.classList.contains('hidden')) {
        revisionContent.classList.remove('hidden')
        revisionHeader.querySelector('.chevron-icon')?.setAttribute('style', 'transform: rotate(180deg)')
      } else {
        revisionContent.classList.add('hidden')
        revisionHeader.querySelector('.chevron-icon')?.removeAttribute('style')
      }
    })
    
    // 添加整个批注框的折叠功能
    header.addEventListener('click', (e) => {
      // 防止触发编辑和删除按钮的事件
      if ((e.target as Element).closest('.edit-btn, .delete-btn')) {
        return
      }
      
      // 折叠/展开整个批注内容
      if (commentBody.style.display === 'none') {
        commentBody.style.display = 'block'
        revisionSection.style.display = 'block'
        header.querySelector('.chevron-icon')?.setAttribute('style', 'transform: rotate(180deg)')
      } else {
        commentBody.style.display = 'none'
        revisionSection.style.display = 'none'
        header.querySelector('.chevron-icon')?.removeAttribute('style')
      }
    })
    
    // 组装内容
    commentBody.appendChild(originalText)
    commentBody.appendChild(commentContent)
    
    // 组装批注气泡
    content.appendChild(header)
    content.appendChild(commentBody)
    content.appendChild(revisionSection)
    
    bubble.appendChild(content)
    
    // 编辑按钮处理
    const editBtn = content.querySelector('.edit-btn')
    if (editBtn) {
      editBtn.addEventListener('click', (e) => {
        e.stopPropagation()

        // 直接获取最新的批注对象
        const latestCommentStore = useCommentStore.getState()
        const latestComment = latestCommentStore.comments.find(c => c.id === id)
        
        if (!latestComment) {
          return
        }
        
        // 设置为活动批注
        latestCommentStore.setActiveComment(id)
        
        // 查找内容区域
        const contentDiv = commentBody.querySelector('.comment-content')
        if (!contentDiv) {
          return
        }
        
        // 如果已经处于编辑状态，不重复创建
        if (commentBody.querySelector('textarea')) {
          return
        }
        
        // 确保有内容可编辑（直接从store获取）
        const contentToEdit = latestComment.content || ''
        
        // 清空内容区域
        const originalHTML = contentDiv.innerHTML
        contentDiv.innerHTML = ''
        
        // 创建编辑区域
        const textarea = document.createElement('textarea')
        textarea.className = "w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-微软雅黑 resize-none"
        textarea.rows = 4
        textarea.placeholder = "请输入批注内容..."
        textarea.value = contentToEdit
        
        // 创建按钮容器
        const btnContainer = document.createElement('div')
        btnContainer.className = "flex justify-end gap-2 mt-2"
        
        // 创建保存按钮
        const saveBtn = document.createElement('button')
        saveBtn.className = "px-2 py-1 text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 rounded"
        saveBtn.textContent = "保存"
        
        // 创建取消按钮
        const cancelBtn = document.createElement('button')
        cancelBtn.className = "px-2 py-1 text-xs bg-gray-100 text-gray-600 hover:bg-gray-200 rounded"
        cancelBtn.textContent = "取消"
        
        // 保存逻辑
        saveBtn.addEventListener('click', () => {
          const newContent = textarea.value.trim()
          if (newContent) {
            const now = new Date().toISOString()
            
            // 更新store
            const store = useCommentStore.getState()
            store.updateComment(id, {
              content: newContent,
              updatedAt: now
            })
            
            // 更新显示
            contentDiv.innerHTML = `
              <div class="comment-main-content">${newContent}</div>
              <div class="text-xs text-gray-400 mt-2 text-right">更新于：${new Date(now).toLocaleString()}</div>
            `
          } else {
            // 如果内容为空，恢复原始内容
            contentDiv.innerHTML = originalHTML
          }
        })
        
        // 取消逻辑
        cancelBtn.addEventListener('click', () => {
          contentDiv.innerHTML = originalHTML
        })
        
        // 组装UI
        btnContainer.appendChild(cancelBtn)
        btnContainer.appendChild(saveBtn)
        contentDiv.appendChild(textarea)
        contentDiv.appendChild(btnContainer)
        
        // 聚焦
        textarea.focus()
        
        // 调整高度
        setTimeout(() => {
          textarea.style.height = 'auto'
          textarea.style.height = `${Math.max(textarea.scrollHeight, 80)}px`
        }, 0)
        
        // 输入事件
        textarea.addEventListener('input', () => {
          textarea.style.height = 'auto'
          textarea.style.height = `${Math.max(textarea.scrollHeight, 80)}px`
          saveBtn.disabled = !textarea.value.trim()
        })
      })
    }
    
    // 删除按钮处理
    const deleteBtn = content.querySelector('.delete-btn')
    if (deleteBtn) {
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        
        // 在删除批注前，先移除文本高亮
        if (editor && comment.anchor) {
          // 使用与CommentPanel相同的方式，通过TR移除标记
          editor.commands.command(({ tr, dispatch }) => {
            if (dispatch) {
              // 移除comment标记
              tr.removeMark(comment.anchor.from, comment.anchor.to, editor.schema.marks.comment)
              dispatch(tr)
            }
            return true
          })
        }
        
        // 删除批注数据
        deleteComment(id)
        
        // 移除界面元素
        if (bubble.parentNode) {
          bubble.remove()
          delete popupsRef.current[id]
          
          if (markersRef.current[id] && markersRef.current[id].parentNode) {
            markersRef.current[id].remove()
            delete markersRef.current[id]
          }
        }
      })
    }
    
    // 原文点击
    originalText.addEventListener('click', () => {
      if (editor) {
        editor.commands.setTextSelection({
          from: comment.anchor.from,
          to: comment.anchor.to,
        })
        editor.commands.scrollIntoView()
      }
    })
    
    // 添加到容器
    containerRef.current.appendChild(bubble)
    popupsRef.current[id] = bubble
    
    return bubble
  }, [editor, setActiveComment, deleteComment, updateComment, addSuggestion])
  
  // 更新元素位置的函数
  const updateElementsPosition = useCallback(() => {
    const visibleComments = getVisibleComments()
    const currentIds = new Set(visibleComments.map(c => c.id))
    
    // 移除不再可见的批注标记
    Object.keys(markersRef.current).forEach(id => {
      if (!currentIds.has(id) && markersRef.current[id]) {
        markersRef.current[id].remove()
        delete markersRef.current[id]
      }
    })
    
    // 移除不再可见的批注气泡
    Object.keys(popupsRef.current).forEach(id => {
      if (!currentIds.has(id) && popupsRef.current[id]) {
        popupsRef.current[id].remove()
        delete popupsRef.current[id]
      }
    })
    
    // 批量计算所有位置，减少重排
    const positionsMap = new Map()
    
    // 获取编辑器容器
    const editorContainer = document.querySelector('.ProseMirror')?.parentElement
    const editorRect = editorContainer?.getBoundingClientRect() || { right: 0, top: 0, width: 0 }
    
    // 计算右侧空间位置
    const rightMargin = 40 // 右侧边距，从20增加到40，让批注气泡与A4纸张之间保持更大间距
    const commentAreaRight = editorRect.right + rightMargin
    const maxBubbleWidth = 280 // 批注气泡最大宽度
    
    // 更新可见批注的位置
    visibleComments.forEach(({ id, element, index }) => {
      // 获取批注对应的注释
      const comment = comments.find(c => c.id === id)
      if (!comment) return
      
      // 获取元素在视口中的位置
      const { top, left, width, height } = element.getBoundingClientRect()
      
      // 转换为容器相对坐标
      const containerCoords = viewportToContainerCoords(left, top)
      
      // 计算标记位置，显示在批注文本的右侧
      const markerLeft = containerCoords.x + width + 2
      const markerTop = containerCoords.y + height/2 - 7 // 垂直居中
      
      // 计算批注气泡位置
      const bubbleLeft = viewportToContainerCoords(commentAreaRight, 0).x
      const bubbleTop = containerCoords.y + height/2 // 垂直居中
      
      // 存储位置信息
      positionsMap.set(id, {
        markerLeft,
        markerTop,
        bubbleLeft,
        bubbleTop,
        width,
        index,
        comment
      })
    })
    
    // 布局优化 - 防止批注重叠
    // 按垂直位置排序
    const sortedPositions = Array.from(positionsMap.entries())
      .sort((a, b) => a[1].bubbleTop - b[1].bubbleTop)
    
    // 调整垂直位置防止重叠
    const bubbleHeight = 140 // 估计的批注气泡基础高度
    const contentHeightPerChar = 0.06 // 每个字符增加的高度（估算值）
    const bubbleGap = 15 // 批注气泡之间的间距
    
    for (let i = 1; i < sortedPositions.length; i++) {
      const prev = sortedPositions[i-1][1]
      const curr = sortedPositions[i][1]
      
      // 计算上一个批注的估计高度（基于内容长度）
      const prevComment = prev.comment
      const prevContentLength = (prevComment.content?.length || 0) + (prevComment.original?.length || 0)
      const prevHeight = bubbleHeight + (prevContentLength * contentHeightPerChar)
      
      // 如果当前批注与前一个批注重叠
      if (curr.bubbleTop < prev.bubbleTop + prevHeight + bubbleGap) {
        // 将当前批注向下移动
        curr.bubbleTop = prev.bubbleTop + prevHeight + bubbleGap
      }
    }
    
    // 批量应用DOM更新
    sortedPositions.forEach(([id, pos]) => {
      const { markerLeft, markerTop, bubbleLeft, bubbleTop, index, comment } = pos
      
      // 创建或获取标记
      const marker = createMarker(id, index)
      
      // 更新标记位置 - 使用transform而不是left/top以减少重排
      marker.style.transform = `translate(${markerLeft}px, ${markerTop}px)`
      
      // 创建或获取批注气泡
      const bubble = createCommentBubble(id, comment, index)
      
      // 更新批注气泡位置和宽度
      bubble.style.width = `${maxBubbleWidth}px`
      bubble.style.transform = `translate(${bubbleLeft}px, ${bubbleTop}px) translateY(-50%)`
      
      // 移除连接线逻辑，不再创建连接线
    })
  }, [createMarker, createCommentBubble, getVisibleComments, comments, viewportToContainerCoords])
  
  // 处理编辑器滚动和窗口大小变化
  useEffect(() => {
    // 使用节流函数防止频繁更新
    let rafId: number | null = null
    let lastUpdateTime = 0
    const throttleInterval = 100 // 100ms节流间隔
    
    const onUpdate = () => {
      const now = Date.now()
      
      // 如果距离上次更新时间不到节流间隔，则使用RAF延迟执行
      if (now - lastUpdateTime < throttleInterval) {
        if (rafId) cancelAnimationFrame(rafId)
        rafId = requestAnimationFrame(() => {
          updateElementsPosition()
          lastUpdateTime = Date.now()
          rafId = null
        })
      } else {
        // 否则直接执行
        if (rafId) cancelAnimationFrame(rafId)
        rafId = requestAnimationFrame(() => {
          updateElementsPosition()
          lastUpdateTime = Date.now()
          rafId = null
        })
      }
    }
    
    // 注册滚动和大小变化事件 - 使用passive: true优化滚动性能
    window.addEventListener('scroll', onUpdate, { passive: true })
    window.addEventListener('resize', onUpdate)
    
    if (editor && editor.view) {
      editor.view.dom.addEventListener('scroll', onUpdate, { passive: true })
    }
    
    // 编辑器内容变化时更新
    editor?.on('update', onUpdate)
    editor?.on('selectionUpdate', onUpdate)
    
    // 初始化时执行一次更新
    updateElementsPosition()
    lastUpdateTime = Date.now()
    
    // 不再需要频繁的轮询更新，移除定时器
    
    return () => {
      if (rafId) cancelAnimationFrame(rafId)
      window.removeEventListener('scroll', onUpdate)
      window.removeEventListener('resize', onUpdate)
      
      if (editor && editor.view) {
        editor.view.dom.removeEventListener('scroll', onUpdate)
      }
      
      editor?.off('update', onUpdate)
      editor?.off('selectionUpdate', onUpdate)
    }
  }, [editor, updateElementsPosition])

  return null;
}

// 批注标记渲染组件
export function CommentMarkerRenderer({
  editor,
  getPos,
  node,
  popupComponent,
  onUpdateComment
}: CommentMarkerProps) {
  const { 
    comments, 
    activeCommentId, 
    setActiveComment, 
    deleteComment, 
    setCommentState,
    addSuggestion,
    updateComment 
  } = useCommentStore()
  
  const [newSuggestion, setNewSuggestion] = useState<string>('')
  const [visiblePopupId, setVisiblePopupId] = useState<string | null>(null)
  const markersRef = useRef<{[key: string]: HTMLElement}>({})
  const connectorsRef = useRef<{[key: string]: HTMLElement}>({})
  const popupsRef = useRef<{[key: string]: HTMLElement}>({})
  const containerRef = useRef<HTMLDivElement | null>(null)
  const markerRef = useRef<HTMLDivElement | null>(null)
  const popupRef = useRef<HTMLDivElement | null>(null)
  const connectorRef = useRef<HTMLDivElement | null>(null)
  const isPopupVisibleRef = useRef(false)
  
  // 创建DOM容器
  useEffect(() => {
    // 创建一个专门的容器来放置所有批注相关元素
    const container = document.createElement('div')
    container.className = 'comment-markers-container'
    container.style.position = 'absolute'
    container.style.top = '0'
    container.style.left = '0'
    container.style.width = '100%'
    container.style.height = '100%'
    container.style.pointerEvents = 'none'
    container.style.zIndex = '90' // 设置z-index小于工具栏(100)，但高于普通内容
    
    // 找到编辑器容器并插入我们的容器为其第一个子元素
    const editorContainer = document.querySelector('.ProseMirror')?.parentElement
    if (editorContainer) {
      // 将容器设置为相对定位以便内部元素可以正确定位
      if(window.getComputedStyle(editorContainer).position === 'static') {
        editorContainer.style.position = 'relative'
      }
      
      if (editorContainer.firstChild) {
        editorContainer.insertBefore(container, editorContainer.firstChild)
      } else {
        editorContainer.appendChild(container)
      }
      containerRef.current = container
    } else {
      document.body.appendChild(container)
      containerRef.current = container
    }
    
    return () => {
      // 清理容器
      container.remove()
    }
  }, [])
  
  // 视口坐标转换为容器相对坐标
  const viewportToContainerCoords = useCallback((x: number, y: number) => {
    if (!containerRef.current) return { x, y }
    
    const containerRect = containerRef.current.getBoundingClientRect()
    return {
      x: x - containerRect.left,
      y: y - containerRect.top
    }
  }, [])
  
  // 通过比较DOM位置获取当前视图中的所有批注
  const getVisibleComments = useCallback(() => {
    if (!editor || !editor.view || !editor.state) return []
    
    // 获取所有带有data-comment-id的元素
    const commentElements = document.querySelectorAll('[data-comment-id]')
    const visibleComments: { id: string; element: Element; index: number }[] = []
    
    // 获取批注数组，基于其索引位置设置序号
    const { comments } = useCommentStore.getState()
    
    commentElements.forEach((element) => {
      const commentId = element.getAttribute('data-comment-id')
      if (commentId) {
        // 确保批注在我们的存储中
        const comment = comments.find(c => c.id === commentId)
        if (comment) {
          // 查找评论在当前评论数组中的索引位置
          const commentIndex = comments.findIndex(c => c.id === commentId)
          visibleComments.push({ 
            id: commentId, 
            element, 
            // 从1开始，保持与CommentPanel一致的编号
            index: commentIndex + 1 
          })
        }
      }
    })
    
    return visibleComments
  }, [editor, comments])
  
  // 处理点击批注标记的事件
  const handleMarkerClick = useCallback((commentId: string, event: MouseEvent) => {
    // 阻止事件冒泡，这样父级的pointerEvents: none不会阻止点击
    event.stopPropagation()
    
    if (visiblePopupId === commentId) {
      setVisiblePopupId(null)
    } else {
      setVisiblePopupId(commentId)
      setActiveComment(commentId)
    }
  }, [setActiveComment, visiblePopupId])

  // 处理批注内容更新
  const handleUpdateContent = useCallback((id: string, content: string) => {
    updateComment(id, { content });
  }, [updateComment]);
  
  // 计算文本差异
  const calculateDiff = useCallback((original: string, suggested: string) => {
    // 简单的差异显示，实际项目中可能需要使用diff-match-patch等库
    return <span>{suggested}</span>
  }, [])

  // 应用修订
  const handleApplyRevision = useCallback((commentId: string, text: string) => {
    const comment = comments.find(c => c.id === commentId)
    if (!comment || !editor || !text.trim()) return
    
    editor.commands.command(({ tr, dispatch }) => {
      if (dispatch && text.trim()) {
        // 替换文本内容 - 确保文本不为空
        tr.replaceWith(comment.anchor.from, comment.anchor.to, editor.schema.text(text))
        
        // 重新应用批注标记，确保新文本也有高亮和下划线样式
        const commentMark = editor.schema.marks.comment.create({ id: commentId })
        tr.addMark(comment.anchor.from, comment.anchor.from + text.length, commentMark)
        
        dispatch(tr)
      }
      return true
    })
    
    // 更新批注中的原始文本
    updateComment(commentId, {
      original: text,
    })
  }, [editor, comments, updateComment])

  // 处理添加修订建议
  const handleAddSuggestion = useCallback((commentId: string) => {
    if (newSuggestion.trim()) {
      addSuggestion(commentId, newSuggestion)
      setNewSuggestion('')
    }
  }, [newSuggestion, addSuggestion])

  // 处理点击批注内容，让编辑器定位到批注位置
  const handleCommentClick = useCallback((comment: any) => {
    if (editor) {
      editor.commands.setTextSelection({
        from: comment.anchor.from,
        to: comment.anchor.to,
      })
      editor.commands.scrollIntoView()
    }
  }, [editor])
  
  // 创建一个批注标记
  const createMarker = useCallback((id: string, index: number) => {
    // 如果已经存在就返回，或者容器不存在就退出
    if (markersRef.current[id] || !containerRef.current) return markersRef.current[id]
    
    const marker = document.createElement('div')
    marker.className = 'comment-marker'
    marker.dataset.commentId = id
    marker.style.position = 'absolute' // 使用absolute相对于容器定位
    marker.style.width = '14px'
    marker.style.height = '14px'
    marker.style.borderRadius = '50%'
    marker.style.backgroundColor = '#D32F2F'
    marker.style.color = '#fff'
    marker.style.fontSize = '9px'
    marker.style.fontFamily = 'Arial'
    marker.style.display = 'flex'
    marker.style.justifyContent = 'center'
    marker.style.alignItems = 'center'
    marker.style.cursor = 'pointer'
    marker.style.userSelect = 'none'
    marker.style.pointerEvents = 'auto' // 允许接收点击事件
    marker.style.zIndex = '91' // 确保标记在连接线之上
    marker.innerText = `${index}`
    
    // 添加点击事件处理
    marker.addEventListener('click', (e) => handleMarkerClick(id, e as MouseEvent))
    
    // 添加到容器
    containerRef.current.appendChild(marker)
    markersRef.current[id] = marker
    
    return marker
  }, [handleMarkerClick])
  
  // 创建批注弹出框
  const createPopup = useCallback((id: string, comment: any, index: number) => {
    // 如果已经存在就返回，或者容器不存在就退出
    if (popupsRef.current[id] || !containerRef.current) return popupsRef.current[id]
    
    const popup = document.createElement('div')
    popup.className = 'comment-popup'
    popup.style.position = 'absolute' // 使用absolute相对于容器定位
    popup.style.width = '320px'
    popup.style.pointerEvents = 'auto' // 允许接收点击事件
    popup.style.zIndex = '92' // 确保弹出框在标记之上
    
    // 渲染批注内容
    const div = document.createElement('div')
    div.className = 'border rounded-md overflow-hidden shadow-md bg-white'
    div.innerHTML = `
      <div class="bg-neutral-50 p-3 flex justify-between items-center border-b border-gray-200">
        <div class="flex items-center gap-2">
          <div class="h-5 w-5 rounded-full bg-red-600 flex items-center justify-center text-xs text-white">${index}</div>
          <span class="text-sm font-medium">批注</span>
        </div>
        <button class="text-gray-500 hover:text-gray-700">×</button>
      </div>
      <div class="p-3">
        <div class="p-2 bg-[#FFF3CD] rounded text-sm mb-2 comment-original">${comment.original}</div>
        <div class="text-sm text-gray-700 p-2 font-微软雅黑 comment-content">
          <div class="comment-main-content">${comment.content || '无批注内容'}</div>
          ${comment.updatedAt ? `<div class="text-xs text-gray-400 mt-2 text-right">更新于：${new Date(comment.updatedAt).toLocaleString()}</div>` : ''}
        </div>
      </div>
      <div class="border-t border-gray-200 p-2 bg-gray-50 flex gap-2">
        <button class="text-xs text-gray-600 hover:text-gray-800 edit-btn">编辑</button>
        <span class="text-gray-300">|</span>
        <button class="text-xs text-gray-600 hover:text-gray-800 hide-btn">隐藏本批注</button>
        <span class="text-gray-300">|</span>
        <button class="text-xs text-red-600 hover:text-red-800 delete-btn">删除</button>
      </div>
    `
    
    // 添加关闭事件
    const closeButton = div.querySelector('button')
    if (closeButton) {
      closeButton.addEventListener('click', (e) => {
        e.stopPropagation()
        setVisiblePopupId(null)
      })
    }
    
    // 添加删除事件
    const deleteButton = div.querySelectorAll('button')[2]
    if (deleteButton) {
      deleteButton.addEventListener('click', (e) => {
        e.stopPropagation()
        
        // 在删除批注前，先移除文本高亮
        if (editor && comment) {
          // 使用转换(TR)方式移除标记
          editor.commands.command(({ tr, dispatch }) => {
            if (dispatch) {
              // 移除comment标记
              tr.removeMark(comment.anchor.from, comment.anchor.to, editor.schema.marks.comment)
              dispatch(tr)
            }
            return true
          })
        }
        
        // 删除批注数据和关闭弹出框
        deleteComment(id)
        setVisiblePopupId(null)
      })
    }
    
    popup.appendChild(div)
    
    // 添加到容器
    containerRef.current.appendChild(popup)
    popupsRef.current[id] = popup
    
    return popup
  }, [deleteComment, setVisiblePopupId])
  
  // 更新元素位置的函数
  const updateElementsPosition = useCallback(() => {
    const visibleComments = getVisibleComments()
    const currentIds = new Set(visibleComments.map(c => c.id))
    
    // 移除不再可见的批注标记
    Object.keys(markersRef.current).forEach(id => {
      if (!currentIds.has(id) && markersRef.current[id]) {
        markersRef.current[id].remove()
        delete markersRef.current[id]
      }
    })
    
    // 移除不再可见的弹出框
    Object.keys(popupsRef.current).forEach(id => {
      if ((!currentIds.has(id) || visiblePopupId !== id) && popupsRef.current[id]) {
        popupsRef.current[id].remove()
        delete popupsRef.current[id]
      }
    })
    
    // 批量计算所有位置，减少重排
    const positionsMap = new Map()
    
    // 更新可见批注的位置
    visibleComments.forEach(({ id, element, index }) => {
      // 获取批注对应的注释
      const comment = comments.find(c => c.id === id)
      if (!comment) return
      
      // 获取元素在视口中的位置
      const { top, left, width, height } = element.getBoundingClientRect()
      
      // 转换为容器相对坐标
      const containerCoords = viewportToContainerCoords(left, top)
      
      // 计算标记位置，显示在批注文本的右侧
      const markerLeft = containerCoords.x + width
      const markerTop = containerCoords.y
      
      // 存储位置信息
      positionsMap.set(id, {
        markerLeft,
        markerTop,
        width,
        index,
        comment
      })
    })
    
    // 批量应用DOM更新
    positionsMap.forEach((pos, id) => {
      const { markerLeft, markerTop, index, comment } = pos
      
      // 创建或获取标记
      const marker = createMarker(id, index)
      
      // 更新标记位置 - 使用transform而不是left/top以减少重排
      marker.style.transform = `translate(${markerLeft}px, ${markerTop}px)`
      
      // 如果这个批注正在显示弹出框
      if (visiblePopupId === id) {
        // 创建或获取弹出框
        const popup = createPopup(id, comment, index)
        
        // 计算弹出框位置，显示在编辑器右侧
        const editorRect = document.querySelector('.ProseMirror')?.getBoundingClientRect() || { right: window.innerWidth - 350, top: 0 }
        
        // 转换为容器相对坐标
        const popupX = viewportToContainerCoords(editorRect.right + 40, 0).x
        const popupY = Math.max(markerTop - 50, 10) // 稍微向上一些，但不超出顶部
        
        // 更新弹出框位置
        popup.style.transform = `translate(${popupX}px, ${popupY}px)`
      }
    })
  }, [createPopup, getVisibleComments, visiblePopupId, comments, viewportToContainerCoords])
  
  // 当批注ID变化时，更新弹出框状态
  useEffect(() => {
    updateElementsPosition()
  }, [visiblePopupId, updateElementsPosition])
  
  // 处理编辑器滚动和窗口大小变化
  useEffect(() => {
    // 使用节流函数防止频繁更新
    let rafId: number | null = null
    let lastUpdateTime = 0
    const throttleInterval = 100 // 100ms节流间隔
    
    const onUpdate = () => {
      const now = Date.now()
      
      // 如果距离上次更新时间不到节流间隔，则使用RAF延迟执行
      if (now - lastUpdateTime < throttleInterval) {
        if (rafId) cancelAnimationFrame(rafId)
        rafId = requestAnimationFrame(() => {
          updateElementsPosition()
          lastUpdateTime = Date.now()
          rafId = null
        })
      } else {
        // 否则直接执行
        if (rafId) cancelAnimationFrame(rafId)
        rafId = requestAnimationFrame(() => {
          updateElementsPosition()
          lastUpdateTime = Date.now()
          rafId = null
        })
      }
    }
    
    // 注册滚动和大小变化事件 - 使用passive: true优化滚动性能
    window.addEventListener('scroll', onUpdate, { passive: true })
    window.addEventListener('resize', onUpdate)
    
    if (editor && editor.view) {
      editor.view.dom.addEventListener('scroll', onUpdate, { passive: true })
    }
    
    // 编辑器内容变化时更新
    editor?.on('update', onUpdate)
    editor?.on('selectionUpdate', onUpdate)
    
    // 初始化时执行一次更新
    updateElementsPosition()
    lastUpdateTime = Date.now()
    
    // 不再需要频繁的轮询更新，移除定时器
    
    return () => {
      if (rafId) cancelAnimationFrame(rafId)
      window.removeEventListener('scroll', onUpdate)
      window.removeEventListener('resize', onUpdate)
      
      if (editor && editor.view) {
        editor.view.dom.removeEventListener('scroll', onUpdate)
      }
      
      editor?.off('update', onUpdate)
      editor?.off('selectionUpdate', onUpdate)
    }
  }, [editor, updateElementsPosition])
  
  // 组件返回空，因为我们直接在DOM中创建和管理元素
  return null
} 