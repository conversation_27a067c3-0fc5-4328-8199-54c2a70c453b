import { Mark, markPasteRule, mergeAttributes } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { generateUniqueId } from '@/lib/utils'

export interface CommentOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    comment: {
      /**
       * 为选中的文本添加批注
       */
      setComment: (id: string) => ReturnType
      /**
       * 移除批注
       */
      unsetComment: () => ReturnType
    }
  }
}

export const Comment = Mark.create<CommentOptions>({
  name: 'comment',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: element => element.getAttribute('data-comment-id'),
        renderHTML: attributes => {
          if (!attributes.id) {
            return {}
          }

          return {
            'data-comment-id': attributes.id,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-comment-id]',
        getAttrs: node => {
          if (typeof node === 'string') {
            return false
          }
          // 返回null表示匹配
          return {}
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(
        { 
          class: 'comment-mark', 
          style: 'background-color: #FFF3CD; border-bottom: 1px dashed #2563EB; text-decoration: none;' 
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      0,
    ]
  },

  addCommands() {
    return {
      setComment:
        id =>
        ({ commands }) => {
          const commentId = id || generateUniqueId()
          return commands.setMark(this.name, { id: commentId })
        },
      unsetComment:
        () =>
        ({ commands }) => {
          return commands.unsetMark(this.name)
        },
    }
  },

  addPasteRules() {
    return [
      markPasteRule({
        find: /\[\[([^\]]+)\]\]/g,
        type: this.type,
        getAttributes: () => {
          return {
            id: generateUniqueId(),
          }
        },
      }),
    ]
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('commentPlugin'),
        props: {
          decorations(state) {
            return null
          },
        },
      }),
    ]
  },
}) 