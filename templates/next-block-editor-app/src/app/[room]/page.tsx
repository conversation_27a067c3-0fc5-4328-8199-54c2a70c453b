'use client'

import { TiptapCollabProvider } from '@hocuspocus/provider'
import 'iframe-resizer/js/iframeResizer.contentWindow'
import { useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { Doc as YDoc } from 'yjs'

import { BlockEditor } from '@/components/BlockEditor'
import { createPortal } from 'react-dom'
import { Surface } from '@/components/ui/Surface'
import { Toolbar } from '@/components/ui/Toolbar'
import { Icon } from '@/components/ui/Icon'
import { useCollaboration } from '@/hooks/useCollaboration'

export default function Document({ params }: { params: { room: string } }) {
  const searchParams = useSearchParams()
  const providerState = useCollaboration({
    docId: params.room,
    enabled: parseInt(searchParams?.get('noCollab') as string) !== 1,
  })
  
  // 滚动容器引用
  const editorContentRef = useRef<HTMLElement | null>(null);

  // 回到顶部功能
  const scrollToTop = useCallback(() => {
    // 获取编辑器内容容器
    if (!editorContentRef.current) {
      const editorContent = document.querySelector('.flex-1.overflow-y-auto');
      if (editorContent) {
        editorContentRef.current = editorContent as HTMLElement;
      }
    }
    
    // 如果找到滚动容器，则滚动它
    if (editorContentRef.current) {
      editorContentRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      // 否则尝试滚动窗口
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, []);


  if (providerState.state === 'loading') return

  const ScrollToTopButton = createPortal(
    <Surface className="flex items-center gap-1 fixed bottom-16 right-6 z-[99999] p-1">
      <Toolbar.Button 
        onClick={scrollToTop} 
        tooltip="回到顶部"
        className="hover:bg-transparent focus:bg-transparent active:bg-transparent"
      >
        <Icon name="ArrowUp" />
      </Toolbar.Button>
    </Surface>,
    document.body,
  )

  return (
    <>
      {ScrollToTopButton}
      <BlockEditor ydoc={providerState.yDoc} provider={providerState.provider} />
    </>
  )
}
