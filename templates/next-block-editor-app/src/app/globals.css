@tailwind base;
@tailwind components;
@tailwind utilities;

@import './editor.css';

@layer base {
  body {
    @apply bg-white text-neutral-900;
    @apply antialiased;

    font-size: 16px;
  }
}

html,
body {
  @apply bg-white text-neutral-900;
}

input::placeholder,
textarea::placeholder {
  @apply text-black/50;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  @apply text-black/50;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  @apply text-black/40;
}

.react-colorful {
  width: 100% !important;
}

[data-reference-hidden] {
  opacity: 0;
  pointer-events: none;
}

::-webkit-scrollbar {
  @apply w-1 h-1 bg-neutral-500/20;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-500/50 rounded-full;
}

input[type='range'] {
  @apply h-2.5 bg-neutral-200 border-0 rounded appearance-none active:bg-neutral-300 transition-all;

  &::-webkit-slider-thumb {
    @apply appearance-none w-3 h-5 bg-neutral-800 rounded-full active:bg-neutral-900 active:w-4 active:h-6 transition-all;
  }
}

/* 保留dark模式选择器，以便手动切换时使用 */
.dark {
  @apply bg-black text-white;
}

.ghost-text {
  color: #bdbdbd !important;
  font-style: italic;
  opacity: 0.6;
  pointer-events: none;
  user-select: none;
}
