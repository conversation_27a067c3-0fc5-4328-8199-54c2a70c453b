import { EditorContent } from '@tiptap/react'
import React, { useRef, useState, useCallback } from 'react'

import { LinkMenu } from '@/components/menus'

import { useBlockEditor } from '@/hooks/useBlockEditor'

import '@/styles/index.css'

import { Sidebar } from '@/components/Sidebar'
import ImageBlockMenu from '@/extensions/ImageBlock/components/ImageBlockMenu'
import { ColumnsMenu } from '@/extensions/MultiColumn/menus'
import { TableColumnMenu, TableRowMenu } from '@/extensions/Table/menus'
import { TopNavigation, FormattingToolbar } from './components'
import { StatusBar } from './components/StatusBar'
import { TextMenu } from '../menus/TextMenu'
import { ContentItemMenu } from '../menus/ContentItemMenu'
import { useSidebar } from '@/hooks/useSidebar'
import * as Y from 'yjs'
import { TiptapCollabProvider } from '@hocuspocus/provider'
import { CommentMarker } from '@/extensions/Comment/CommentMarker'
import { CommentMenu } from '@/components/menus/CommentMenu'
import { CommentPanel } from '@/components/panels/CommentPanel'
import { useCommentStore } from '@/lib/stores/commentStore'
import { useAutocompletion } from '@/hooks/useAutocompletion'

export const BlockEditor = ({
  aiToken,
  ydoc,
  provider,
}: {
  aiToken?: string
  ydoc: Y.Doc | null
  provider?: TiptapCollabProvider | null | undefined
}) => {
  const [isEditable, setIsEditable] = useState(true)
  const [autocompletionEnabled, setAutocompletionEnabled] = useState(false)
  const [aiSuggestionEnabled, setAiSuggestionEnabled] = useState(true)
  const ghostTextRef = useRef<{ text: string; pos: number } | null>(null)
  const [_, forceUpdate] = useState(0)
  const menuContainerRef = useRef(null)

  const leftSidebar = useSidebar()
  const getGhostText = useCallback(() => ghostTextRef.current, [])
  const onClearGhost = useCallback(() => {
    ghostTextRef.current = null
    setAutocompletionEnabled(false)
    forceUpdate(x => x + 1)
  }, [])
  const { editor, users, collabState } = useBlockEditor({
    ydoc,
    provider,
    getGhostText,
    onClearGhost,
    onTransaction({ editor: currentEditor }) {
      setIsEditable(currentEditor.isEditable)
    },
  })

  const { showCommentPanel, toggleCommentPanel } = useCommentStore()
  const { getSuggestion } = useAutocompletion()

  // Tab自动补全逻辑
  React.useEffect(() => {
    if (!editor || !autocompletionEnabled) return
    const dom = editor.view.dom as HTMLElement
    if (!dom) return
    const handleKeyDown = async (e: KeyboardEvent) => {
      if (e.key === 'Tab' && !e.shiftKey && !e.altKey && !e.ctrlKey && !e.metaKey) {
        e.preventDefault()
        e.stopImmediatePropagation()
        if (!ghostTextRef.current) {
          // 第一次Tab，异步生成补全
          const suggestion = await getSuggestion(editor)
          if (!suggestion) return
          ghostTextRef.current = { text: suggestion, pos: editor.state.selection.from }
          // 强制触发selection transaction，刷新decoration
          editor.view.dispatch(editor.state.tr.setSelection(editor.state.selection))
          forceUpdate(x => x + 1)
        } else {
          // 第二次Tab，接受补全
          editor.chain().focus().insertContent(ghostTextRef.current.text).run()
          ghostTextRef.current = null
          // 强制触发selection transaction，刷新decoration
          editor.view.dispatch(editor.state.tr.setSelection(editor.state.selection))
          forceUpdate(x => x + 1)
        }
      } else if (e.key === 'Escape') {
        ghostTextRef.current = null
        editor.view.dispatch(editor.state.tr.setSelection(editor.state.selection))
        forceUpdate(x => x + 1)
      }
    }
    dom.addEventListener('keydown', handleKeyDown, true)
    return () => {
      dom.removeEventListener('keydown', handleKeyDown, true)
    }
  }, [editor, autocompletionEnabled, getSuggestion])

  // 编辑器内容变动时清除ghost
  React.useEffect(() => {
    if (!editor) return
    const clear = (tr?: any) => {
      // 只在内容实际变动时清空ghostText
      if (tr && tr.docChanged) {
        console.log('[transaction] clear ghostText')
        ghostTextRef.current = null
        forceUpdate(x => x + 1)
      }
    }
    editor.on('transaction', clear)
    return () => {
      editor.off('transaction', clear)
    }
  }, [editor])

  if (!editor || !users) {
    return null
  }

  return (
    <div className="flex flex-col h-full" ref={menuContainerRef}>
      <TopNavigation
        editor={editor}
        autocompletionEnabled={autocompletionEnabled}
        setAutocompletionEnabled={setAutocompletionEnabled}
        aiSuggestionEnabled={aiSuggestionEnabled}
        setAiSuggestionEnabled={setAiSuggestionEnabled}
      />
      
      <div className="flex h-full flex-1 overflow-hidden">
        <Sidebar isOpen={leftSidebar.isOpen} onClose={leftSidebar.close} editor={editor} />
        <div className="relative flex flex-col flex-1 h-full overflow-hidden">
          <div className="flex-1 overflow-y-auto bg-neutral-100 flex justify-center pt-[10px] pb-6">
            <div>
              <div 
                className="relative"
                style={{
                  width: '210mm',
                  minHeight: '297mm',
                  backgroundColor: 'white',
                  boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
                  padding: '20px'
                }}
              >
                {/* 左上角标记 */}
                <div className="absolute left-16 top-10 pointer-events-none text-gray-300">
                  <svg width="60" height="60" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5,5 L5,0.5 M5,5 L0.5,5" stroke="currentColor" strokeWidth="0.15"/>
                  </svg>
                </div>
                
                {/* 右上角标记 */}
                <div className="absolute right-16 top-10 pointer-events-none text-gray-300">
                  <svg width="60" height="60" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5,5 L5,0.5 M5,5 L9.5,5" stroke="currentColor" strokeWidth="0.15"/>
                  </svg>
                </div>
                
                {/* 左下角标记 */}
                <div className="absolute left-16 bottom-10 pointer-events-none text-gray-300">
                  <svg width="60" height="60" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5,5 L5,9.5 M5,5 L0.5,5" stroke="currentColor" strokeWidth="0.15"/>
                  </svg>
                </div>
                
                {/* 右下角标记 */}
                <div className="absolute right-16 bottom-10 pointer-events-none text-gray-300">
                  <svg width="60" height="60" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5,5 L5,9.5 M5,5 L9.5,5" stroke="currentColor" strokeWidth="0.15"/>
                  </svg>
                </div>
                
                <EditorContent editor={editor} className="h-full" />
              </div>
            </div>
          </div>
          
          <StatusBar 
            editor={editor} 
            isSidebarOpen={leftSidebar.isOpen}
            toggleSidebar={leftSidebar.toggle}
            collabState={collabState}
            users={users}
          />
          
          <ContentItemMenu editor={editor} isEditable={isEditable} />
          <LinkMenu editor={editor} appendTo={menuContainerRef} />
          <TextMenu editor={editor} />
          <ColumnsMenu editor={editor} appendTo={menuContainerRef} />
          <TableRowMenu editor={editor} appendTo={menuContainerRef} />
          <TableColumnMenu editor={editor} appendTo={menuContainerRef} />
          <ImageBlockMenu editor={editor} appendTo={menuContainerRef} />
          
          {/* 批注相关组件 */}
          <CommentMarker editor={editor} />
          <CommentMenu editor={editor} />
        </div>
      </div>
    </div>
  )
}

export default BlockEditor
