'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Editor } from '@tiptap/react'
import { Icon } from '@/components/ui/Icon'
import { Toolbar } from '@/components/ui/Toolbar'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import { Surface } from '@/components/ui/Surface'
import { useCommentStore } from '@/lib/stores/commentStore'

export interface FormattingToolbarProps {
  editor: Editor
}

export const FormattingToolbar: React.FC<FormattingToolbarProps> = ({ editor }) => {
  // 添加状态来跟踪是否显示换行符
  const [showInvisibles, setShowInvisibles] = useState(false);
  const { toggleCommentPanel } = useCommentStore()

  // 更新状态的处理函数
  const updateInvisibleState = useCallback(() => {
    if (editor && editor.storage.invisibleCharacters) {
      setShowInvisibles(editor.storage.invisibleCharacters.isVisible);
    }
  }, [editor]);

  // 同步编辑器的invisible状态
  useEffect(() => {
    if (editor) {
      // 初始同步
      updateInvisibleState();
      
      // 监听编辑器的事务和更新事件
      editor.on('transaction', updateInvisibleState);
      editor.on('update', updateInvisibleState);
      
      return () => {
        // 组件卸载时清理事件监听
        editor.off('transaction', updateInvisibleState);
        editor.off('update', updateInvisibleState);
      };
    }
  }, [editor, updateInvisibleState]);

  // 切换显示/隐藏换行符
  const handleToggleInvisibles = () => {
    if (editor) {
      try {
        // 直接执行命令，不使用can()检查
        editor.chain().focus().toggleInvisibleCharacters().run();
        
        // 在下一个事件循环更新状态，确保与编辑器状态同步
        setTimeout(() => {
          if (editor.storage.invisibleCharacters) {
            setShowInvisibles(editor.storage.invisibleCharacters.isVisible);
          }
        }, 10);
      } catch (error) {
        // 移除控制台日志
        console.error('切换换行符状态失败:', error)
      }
    }
  };

  return (
    <div className="flex items-center w-full p-1 border-b border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black overflow-x-auto z-[100]">
      <div className="flex flex-wrap items-center gap-0.5">
        {/* 字体选择 */}
        <Dropdown.Root>
          <Dropdown.Trigger asChild>
            <Toolbar.Button>
              <span className="text-xs mr-1">微软雅黑</span>
              <Icon name="ChevronDown" className="w-3 h-3" />
            </Toolbar.Button>
          </Dropdown.Trigger>
          <Dropdown.Content asChild>
            <Surface className="p-1 min-w-[120px]">
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                微软雅黑
              </Dropdown.Item>
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                宋体
              </Dropdown.Item>
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                Arial
              </Dropdown.Item>
            </Surface>
          </Dropdown.Content>
        </Dropdown.Root>

        {/* 字号选择 */}
        <Dropdown.Root>
          <Dropdown.Trigger asChild>
            <Toolbar.Button>
              <span className="text-xs mr-1">14</span>
              <Icon name="ChevronDown" className="w-3 h-3" />
            </Toolbar.Button>
          </Dropdown.Trigger>
          <Dropdown.Content asChild>
            <Surface className="p-1 min-w-[80px]">
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                12
              </Dropdown.Item>
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                14
              </Dropdown.Item>
              <Dropdown.Item className="flex items-center p-1.5 text-sm hover:bg-neutral-100 rounded cursor-pointer">
                16
              </Dropdown.Item>
            </Surface>
          </Dropdown.Content>
        </Dropdown.Root>

        <Toolbar.Divider />

        {/* 文本格式化按钮 */}
        <Toolbar.Button 
          tooltip="加粗" 
          onClick={() => editor.chain().focus().toggleBold().run()}
          active={editor.isActive('bold')}
        >
          <Icon name="Bold" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="斜体" 
          onClick={() => editor.chain().focus().toggleItalic().run()}
          active={editor.isActive('italic')}
        >
          <Icon name="Italic" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="下划线" 
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          active={editor.isActive('underline')}
        >
          <Icon name="Underline" />
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 对齐方式 */}
        <Toolbar.Button 
          tooltip="左对齐" 
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
          active={editor.isActive({ textAlign: 'left' })}
        >
          <Icon name="AlignLeft" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="居中对齐" 
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
          active={editor.isActive({ textAlign: 'center' })}
        >
          <Icon name="AlignCenter" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="右对齐" 
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
          active={editor.isActive({ textAlign: 'right' })}
        >
          <Icon name="AlignRight" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="两端对齐" 
          onClick={() => editor.chain().focus().setTextAlign('justify').run()}
          active={editor.isActive({ textAlign: 'justify' })}
        >
          <Icon name="AlignJustify" />
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 列表 */}
        <Toolbar.Button 
          tooltip="无序列表" 
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          active={editor.isActive('bulletList')}
        >
          <Icon name="List" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="有序列表" 
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          active={editor.isActive('orderedList')}
        >
          <Icon name="ListOrdered" />
        </Toolbar.Button>
        
        <Toolbar.Button 
          tooltip="任务列表" 
          onClick={() => editor.chain().focus().toggleTaskList().run()}
          active={editor.isActive('taskList')}
        >
          <Icon name="ListChecks" />
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 缩进 */}
        <Toolbar.Button tooltip="减少缩进">
          <Icon name="ArrowLeft" />
        </Toolbar.Button>
        
        <Toolbar.Button tooltip="增加缩进">
          <Icon name="ArrowRight" />
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 批注按钮 */}
        <Toolbar.Button 
          tooltip="批注" 
          onClick={() => toggleCommentPanel()}
        >
          <Icon name="MessageSquare" />
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 显示/隐藏换行符按钮 */}
        <Toolbar.Button 
          tooltip={showInvisibles ? "隐藏换行符" : "显示换行符"} 
          onClick={handleToggleInvisibles}
          active={showInvisibles}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="16" 
            height="16" 
            viewBox="0 0 16 16" 
            fill="none" 
            className={showInvisibles ? "text-blue-500" : ""}
          >
            <path 
              d="M3 2h10M3 8h5.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M10 5v6" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M10 11l-1.5-1.5M10 11l1.5-1.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M3 14h10" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
          </svg>
        </Toolbar.Button>

        <Toolbar.Divider />

        {/* 颜色选择 */}
        <Dropdown.Root>
          <Dropdown.Trigger asChild>
            <Toolbar.Button tooltip="文字颜色">
              <Icon name="Palette" />
            </Toolbar.Button>
          </Dropdown.Trigger>
          <Dropdown.Content asChild>
            <Surface className="p-1 grid grid-cols-5 gap-1">
              <div className="w-5 h-5 bg-black rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-red-500 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-blue-500 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-green-500 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-yellow-500 rounded cursor-pointer"></div>
            </Surface>
          </Dropdown.Content>
        </Dropdown.Root>

        <Dropdown.Root>
          <Dropdown.Trigger asChild>
            <Toolbar.Button tooltip="高亮颜色">
              <Icon name="Highlighter" />
            </Toolbar.Button>
          </Dropdown.Trigger>
          <Dropdown.Content asChild>
            <Surface className="p-1 grid grid-cols-5 gap-1">
              <div className="w-5 h-5 bg-yellow-200 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-green-200 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-blue-200 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-pink-200 rounded cursor-pointer"></div>
              <div className="w-5 h-5 bg-purple-200 rounded cursor-pointer"></div>
            </Surface>
          </Dropdown.Content>
        </Dropdown.Root>

        <Toolbar.Divider />

        {/* 更多操作按钮 */}
        <Toolbar.Button tooltip="更多格式">
          <Icon name="Menu" />
        </Toolbar.Button>
      </div>
    </div>
  )
} 