import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import AIChatPanel from '@/components/AI/AIChatPanel';
import { ConfigProvider } from 'antd';

/**
 * AI助手按钮组件
 * 点击按钮显示/隐藏A4纸右侧的AI对话面板
 */
const AIAssistantButton: React.FC = () => {
  const [showAIChatPanel, setShowAIChatPanel] = useState(false);
  const [a4Container, setA4Container] = useState<HTMLElement | null>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  
  // 查找A4纸容器
  const findAndUpdateElements = useCallback(() => {
    // 查找A4纸容器
    const a4Elements = document.querySelectorAll('div[style*="210mm"]');
    if (a4Elements.length > 0) {
      setA4Container(a4Elements[0] as HTMLElement);
    }
  }, []);
  
  // 查找A4纸容器
  useEffect(() => {
    findAndUpdateElements();
    
    const observer = new MutationObserver(findAndUpdateElements);
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
    
    return () => observer.disconnect();
  }, [findAndUpdateElements]);
  
  const toggleAIChatPanel = () => {
    setShowAIChatPanel(prev => !prev);
  };
  
  // 处理AI面板关闭
  const handleAIChatClose = () => {
    setShowAIChatPanel(false);
  };
  
  return (
    <>
      {/* AI助手按钮 */}
      <button
        onClick={toggleAIChatPanel}
        className={cn(
          'flex items-center px-3 py-1 rounded text-sm font-medium transition-colors',
          showAIChatPanel
            ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
            : 'text-neutral-600 hover:bg-[#f9f8f7] dark:text-neutral-300 dark:hover:bg-neutral-700'
        )}
        title="AI文档助手"
        aria-label="打开/关闭AI文档助手面板"
      >
        <svg 
          viewBox="0 0 24 24" 
          width="16" 
          height="16" 
          className="mr-1"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M9.5 4.5a1.5 1.5 0 00-3 0 1.5 1.5 0 003 0zM10 8V6a2 2 0 10-4 0v2H2v2h1v9h8V10h1V8h-2zm-2 10H7v-8h1v8zM16.5 4.5a1.5 1.5 0 00-3 0 1.5 1.5 0 003 0zM17 8V6a2 2 0 10-4 0v2h-2v2h1v9h8V10h1V8h-4zm-2 10h-1v-8h1v8zm5-8h1v8h-1v-8z" />
        </svg>
        AI文档助手
      </button>
      
      {/* AI对话面板 */}
      {showAIChatPanel && (
        <div 
          ref={panelRef}
          className="bg-white shadow-sm overflow-hidden border border-gray-200 flex flex-col"
          style={{
            position: 'fixed',
            top: '140px', // 顶部导航(33px) + 工具栏(96px)
            right: a4Container ? undefined : '16px',
            left: a4Container ? `${a4Container.getBoundingClientRect().right + 10}px` : undefined,
            width: '400px',
            height: 'calc(95vh - 140px)', // 视口高度的95%减去顶部位置
            zIndex: 50,
          }}
        >
          {/* 关闭按钮 */}
          {/* <div className="absolute top-2 right-2 z-10">
            <button
              onClick={toggleAIChatPanel}
              className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-500"
              aria-label="关闭AI助手"
            >
              <svg
                viewBox="0 0 24 24"
                width="16"
                height="16"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div> */}
          
          <div className="flex-1 h-full">
            <ConfigProvider
              theme={{
                token: {
                  colorPrimary: '#1677ff',
                  borderRadius: 8,
                },
              }}
            >
              <AIChatPanel onClose={handleAIChatClose} />
            </ConfigProvider>
          </div>
        </div>
      )}
    </>
  );
};

export default AIAssistantButton; 