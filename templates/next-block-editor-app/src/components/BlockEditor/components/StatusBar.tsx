import React, { useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { useEditorState } from '@tiptap/react'
import { Icon } from '@/components/ui/Icon'
import { Toolbar } from '@/components/ui/Toolbar'
import { EditorUser } from '../types'
import { WebSocketStatus } from '@hocuspocus/provider'
import { cn } from '@/lib/utils'
import { getConnectionText } from '@/lib/utils/getConnectionText'
import Tooltip from '@/components/ui/Tooltip'

interface StatusBarProps {
  editor: Editor
  isSidebarOpen?: boolean
  toggleSidebar?: () => void
  collabState?: WebSocketStatus
  users?: EditorUser[]
}

export const StatusBar: React.FC<StatusBarProps> = ({ 
  editor, 
  isSidebarOpen, 
  toggleSidebar,
  collabState = 'disconnected' as WebSocketStatus,
  users = [] 
}) => {
  const { characters, words } = useEditorState({
    editor,
    selector: (ctx): { characters: number; words: number } => {
      const { characters, words } = ctx.editor?.storage.characterCount || { characters: () => 0, words: () => 0 }
      return { characters: characters(), words: words() }
    },
  })

  // 获取当前页数（假设每页约900字符）
  const pageCount = Math.max(1, Math.ceil(characters / 900))
  
  // 获取当前日期
  const today = new Date()
  const formattedDate = today.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
  
  const toggleEditable = useCallback(() => {
    editor.setOptions({ editable: !editor.isEditable })
    // force update the editor
    editor.view.dispatch(editor.view.state.tr)
  }, [editor])

  return (
    <div className="flex items-center justify-between px-4 py-1 border-t border-neutral-200 bg-neutral-50 text-neutral-600 text-xs">
      <div className="flex items-center">
        <Toolbar.Button
          tooltip={isSidebarOpen ? '关闭侧边栏' : '打开侧边栏'}
          onClick={toggleSidebar}
          active={isSidebarOpen}
          className="mr-2"
        >
          <Icon name={isSidebarOpen ? 'PanelLeftClose' : 'PanelLeft'} className="w-4 h-4" />
        </Toolbar.Button>
        <Toolbar.Button 
          tooltip={editor.isEditable ? '禁用编辑' : '启用编辑'} 
          onClick={toggleEditable}
          className="mr-2"
        >
          <Icon name={editor.isEditable ? 'PenOff' : 'Pen'} className="w-4 h-4" />
        </Toolbar.Button>
        <span className="flex items-center mx-2">
          <Icon name="File" className="w-4 h-4 mr-1" />
          {pageCount} 页
        </span>
        <span className="flex items-center mx-2">
          <Icon name="Type" className="w-4 h-4 mr-1" />
          {words} 字
        </span>
        <span className="flex items-center mx-2">
          <Icon name="FileText" className="w-4 h-4 mr-1" />
          {characters} 字符
        </span>
      </div>
      
      <div className="flex items-center">
        <span className="mx-2">
          <Icon name="Calendar" className="w-4 h-4 mr-1 inline-block" />
          {formattedDate}
        </span>
        <span className="mx-2">
          <Icon name="Save" className="w-4 h-4 mr-1 inline-block" />
          已保存
        </span>
        <div className="flex items-center gap-2 ml-2">
          <div
            className={cn('w-2 h-2 rounded-full', {
              'bg-yellow-500': collabState === 'connecting',
              'bg-green-500': collabState === 'connected',
              'bg-red-500': collabState === 'disconnected',
            })}
          />
          <span className="text-xs text-neutral-500 font-semibold">
            {getConnectionText(collabState)}
          </span>
        </div>
        {collabState === 'connected' && users.length > 0 && (
          <div className="flex flex-row items-center">
            <div className="relative flex flex-row items-center ml-3">
              {users.slice(0, 3).map((user: EditorUser) => (
                <div key={user.clientId} className="-ml-2">
                  <Tooltip title={user.name}>
                    <img
                      className="w-6 h-6 border border-white rounded-full"
                      src={`https://api.dicebear.com/7.x/notionists-neutral/svg?seed=${user.name}&backgroundColor=${user.color.replace('#', '')}`}
                      alt="avatar"
                    />
                  </Tooltip>
                </div>
              ))}
              {users.length > 3 && (
                <div className="-ml-2">
                  <div className="flex items-center justify-center w-6 h-6 font-bold text-xs leading-none border border-white bg-[#FFA2A2] rounded-full">
                    +{users.length - 3}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        <span className="flex items-center mx-2">
          <Icon name="Search" className="w-4 h-4 mr-1" />
          100%
        </span>
      </div>
    </div>
  )
}