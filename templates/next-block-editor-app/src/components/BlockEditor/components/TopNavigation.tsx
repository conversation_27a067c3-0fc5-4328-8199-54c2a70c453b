'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Icon } from '@/components/ui/Icon'
import { UmoLogo } from './UmoLogo'
import { Editor } from '@tiptap/react'
import { FONT_FAMILIES, DEFAULT_FONT, FONT_FAMILY_GROUPS, FontGroup, FontOption } from '@/lib/constants/fonts'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import { Surface } from '@/components/ui/Surface'
import { useCommentStore } from '@/lib/stores/commentStore'
import { generateUniqueId } from '@/lib/utils'
import mammoth from 'mammoth'
import AIAssistantButton from './AIAssistantButton'
import { RuleConfigPanel } from '@/extensions/AISuggestion/RuleConfigPanel'

// 定义FontSize类型接口
interface FontSize {
  label: string;
  value: string;
}

// 字体大小选项
const fontSizeOptions: FontSize[] = [
  // 中文字号
  { label: '初号', value: '42px' },
  { label: '小初', value: '36px' },
  { label: '一号', value: '26px' },
  { label: '小一', value: '24px' },
  { label: '二号', value: '22px' },
  { label: '小二', value: '18px' },
  { label: '三号', value: '16px' },
  { label: '小三', value: '15px' },
  { label: '四号', value: '14px' },
  { label: '小四', value: '12px' },
  { label: '五号', value: '10.5px' },
  { label: '小五', value: '9px' },
  { label: '六号', value: '7.5px' },
  { label: '小六', value: '6.5px' },
  // 数字字号（更常用的尺寸）
  { label: '默认', value: '' },
  { label: '8', value: '8px' },
  { label: '10', value: '10px' },
  { label: '12', value: '12px' },
  { label: '14', value: '14px' },
  { label: '16', value: '16px' },
  { label: '18', value: '18px' },
  { label: '20', value: '20px' },
  { label: '24', value: '24px' },
  { label: '28', value: '28px' },
  { label: '36', value: '36px' },
  { label: '48', value: '48px' },
  { label: '72', value: '72px' },
];

// 获取显示标签
const getDisplayLabel = (value: string): string => {
  if (!value) return '默认';
  
  const fontSize = fontSizeOptions.find(size => size.value === value);
  if (fontSize) return fontSize.label;
  
  // 如果是px值但未在预设列表中，则提取数字部分
  const numericSize = parseInt(value);
  return isNaN(numericSize) ? '默认' : numericSize.toString();
};

// 获取下一个更大的字体大小
const getNextLargerFontSize = (currentSize: string): string => {
  if (!currentSize) return '16px';
  
  const numericValue = parseFloat(currentSize);
  if (isNaN(numericValue)) return '16px';
  
  // 查找当前大小在预设中的位置
  const currentIndex = fontSizeOptions.findIndex(size => 
    parseFloat(size.value) === numericValue
  );
  
  if (currentIndex === -1) {
    // 如果不在预设中，找到下一个更大的预设
    const nextSize = fontSizeOptions.find(size => parseFloat(size.value) > numericValue);
    return nextSize ? nextSize.value : fontSizeOptions[0].value; // 初号是最大的
  } else if (currentIndex > 0) {
    // 返回下一个预设大小（注意中文字号是从大到小排列的）
    return fontSizeOptions[currentIndex - 1].value;
  }
  
  // 已经是最大预设，增加20%
  return `${Math.round(numericValue * 1.2)}px`;
};

// 获取下一个更小的字体大小
const getNextSmallerFontSize = (currentSize: string): string => {
  if (!currentSize) return '14px';
  
  const numericValue = parseFloat(currentSize);
  if (isNaN(numericValue)) return '14px';
  
  // 查找当前大小在预设中的位置
  const currentIndex = fontSizeOptions.findIndex(size => 
    parseFloat(size.value) === numericValue
  );
  
  if (currentIndex === -1) {
    // 如果不在预设中，找到下一个更小的预设
    const prevSize = [...fontSizeOptions].reverse().find(size => parseFloat(size.value) < numericValue);
    return prevSize ? prevSize.value : fontSizeOptions[fontSizeOptions.length - 1].value;
  } else if (currentIndex < fontSizeOptions.length - 1) {
    // 返回下一个预设大小（注意中文字号是从大到小排列的）
    return fontSizeOptions[currentIndex + 1].value;
  }
  
  // 已经是最小预设，减少20%，但不小于5px
  return `${Math.max(5, Math.round(numericValue * 0.8))}px`;
};

type TabItem = {
  id: string
  label: string
}

interface TopNavigationProps {
  editor?: Editor
}

export const TopNavigation: React.FC<TopNavigationProps & {
  autocompletionEnabled: boolean;
  setAutocompletionEnabled: (v: boolean) => void;
  aiSuggestionEnabled: boolean;
  setAiSuggestionEnabled: (v: boolean) => void;
}> = ({ editor, autocompletionEnabled, setAutocompletionEnabled, aiSuggestionEnabled, setAiSuggestionEnabled }) => {
  const [activeTab, setActiveTab] = useState<string>('start')
  const [showRuleConfig, setShowRuleConfig] = useState(false)
  
  const tabs: TabItem[] = [
    { id: 'start', label: '开始' },
    { id: 'insert', label: '插入' },
    { id: 'table', label: '表格' },
    { id: 'tools', label: '工具' },
    { id: 'page', label: '页面' },
    { id: 'export', label: '导出' },
  ]
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  }
  
  const renderContent = () => {
    switch (activeTab) {
      case 'start':
        return <StartTabToolbar editor={editor} />;
      case 'insert':
        return <InsertTabToolbar editor={editor} />;
      case 'table':
        return <TableTabToolbar editor={editor} />;
      case 'tools':
        return <ToolsTabToolbar editor={editor} />;
      case 'page':
        return <PageTabToolbar editor={editor} />;
      case 'export':
        return <ExportTabToolbar editor={editor} />;
      default:
        return null;
    }
  }
  
  return (
    <div className="flex flex-col w-full bg-white dark:bg-neutral-900 shadow-sm">
      {/* 顶部标签栏 - Word风格 */}
      <div className="flex bg-[#f3f2f1] dark:bg-neutral-800 border-b border-[#e1dfdd] dark:border-neutral-700">
        <nav className="flex items-center h-9 px-1 w-full">
          <div className="flex-1 h-9 flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={cn(
                  'px-4 h-full text-sm font-medium transition-colors mx-0.5',
                  activeTab === tab.id
                    ? 'bg-white text-[#106ebe] dark:bg-neutral-900 dark:text-blue-400 border-b-[3px] border-[#106ebe] dark:border-blue-400'
                    : 'text-neutral-600 hover:bg-[#f9f8f7] dark:text-neutral-300 dark:hover:bg-neutral-700'
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
          
          {/* AI助手按钮 */}
          <div className="flex items-center px-2 gap-2">
            <AIAssistantButton />
            <button
              className={cn(
                'px-3 py-1 rounded text-xs font-medium border',
                autocompletionEnabled
                  ? 'bg-blue-100 text-blue-700 border-blue-400'
                  : 'bg-gray-100 text-gray-500 border-gray-300 hover:bg-gray-200'
              )}
              aria-label="自动补全开关"
              tabIndex={0}
              onClick={() => setAutocompletionEnabled(!autocompletionEnabled)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') setAutocompletionEnabled(!autocompletionEnabled)
              }}
            >
              自动补全 {autocompletionEnabled ? '开' : '关'}
            </button>
            <button
              className={cn(
                'px-3 py-1 rounded text-xs font-medium border',
                aiSuggestionEnabled
                  ? 'bg-green-100 text-green-700 border-green-400'
                  : 'bg-gray-100 text-gray-500 border-gray-300 hover:bg-gray-200'
              )}
              aria-label="AI建议开关"
              tabIndex={0}
              onClick={() => setAiSuggestionEnabled(!aiSuggestionEnabled)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') setAiSuggestionEnabled(!aiSuggestionEnabled)
              }}
            >
              AI建议 {aiSuggestionEnabled ? '开' : '关'}
            </button>
            <button
              className="px-3 py-1 rounded text-xs font-medium border bg-purple-100 text-purple-700 border-purple-400 hover:bg-purple-200"
              aria-label="样式规则配置"
              tabIndex={0}
              onClick={() => setShowRuleConfig(true)}
            >
              样式规则
            </button>
          </div>
        </nav>
      </div>
      
      {showRuleConfig && <RuleConfigPanel onClose={() => setShowRuleConfig(false)} />}
      
      {/* 工具栏区域 - Word风格 */}
      <div className="h-24 px-2 flex flex-wrap items-center gap-1 bg-white dark:bg-neutral-900 border-b border-[#e1dfdd] dark:border-neutral-700">
        {renderContent()}
      </div>
    </div>
  )
}

// 自定义组件接口定义
interface ButtonProps {
  icon?: React.ReactNode;
  label?: string;
  tooltip?: string;
  onClick?: () => void;
  active?: boolean;
  disabled?: boolean;
  className?: string;
}

interface ButtonGroupProps {
  children: React.ReactNode;
}

interface ButtonWithDropdownProps extends ButtonProps {
  direction?: 'horizontal' | 'vertical';
  children: React.ReactNode;
}

interface DropdownItemProps {
  icon?: React.ReactNode;
  label: string;
  onClick: () => void;
  active?: boolean;
  shortcut?: string;
}

interface FontDropdownProps {
  value: string;
  onChange: (fontFamily: string, fontLabel: string) => void;
}

interface FontSizeDropdownProps {
  value: string;
  onChange: (fontSize: string) => void;
}

// 自定义组件定义
const Button: React.FC<ButtonProps> = ({ 
  icon, 
  label, 
  tooltip, 
  onClick, 
  active, 
  disabled = false,
  className = ''
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'flex items-center py-1 px-2 rounded text-sm',
        'hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 focus:outline-none focus:ring-1 focus:ring-[#106ebe]',
        active ? 'bg-[#edebe9] dark:bg-neutral-700 text-[#106ebe] dark:text-blue-400' : 'text-neutral-700 dark:text-neutral-300',
        disabled ? 'opacity-50 cursor-not-allowed' : '',
        className
      )}
      title={tooltip}
    >
      {icon && <div className="mr-1">{icon}</div>}
      {label && <span>{label}</span>}
    </button>
  )
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({ children }) => {
  return (
    <div className="flex border rounded overflow-hidden">
      {React.Children.map(children, (child) => {
        if (!React.isValidElement(child)) return child;
        return React.cloneElement(child, {
          className: cn(
            'rounded-none border-r last:border-r-0',
            'first:rounded-l last:rounded-r',
            child.props.className
          )
        });
      })}
    </div>
  )
}

const ButtonWithDropdown: React.FC<ButtonWithDropdownProps> = ({ 
  icon, 
  label, 
  tooltip, 
  onClick, 
  active, 
  disabled = false, 
  className = '',
  direction = 'horizontal',
  children 
}) => {
  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <button
          onClick={onClick}
          disabled={disabled}
          className={cn(
            'flex items-center py-1 px-2 rounded text-sm',
            'hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 focus:outline-none focus:ring-1 focus:ring-[#106ebe]',
            active ? 'bg-[#edebe9] dark:bg-neutral-700 text-[#106ebe] dark:text-blue-400' : 'text-neutral-700 dark:text-neutral-300',
            disabled ? 'opacity-50 cursor-not-allowed' : '',
            className
          )}
          title={tooltip}
        >
          {icon && <div className="mr-1">{icon}</div>}
          {label && <span className="mr-1">{label}</span>}
          <Icon name="ChevronDown" className="h-3 w-3" />
        </button>
      </Dropdown.Trigger>
      <Dropdown.Content side="bottom" align="start" className="z-50">
        <Surface className="p-1 min-w-[150px]">
          {children}
        </Surface>
      </Dropdown.Content>
    </Dropdown.Root>
  )
}

const DropdownItem: React.FC<DropdownItemProps> = ({ 
  icon, 
  label, 
  onClick, 
  active = false, 
  shortcut = ''
}) => {
  return (
    <Dropdown.Item asChild>
      <button
        onClick={onClick}
        className={cn(
          'flex items-center w-full text-left p-1.5 text-sm rounded',
          'hover:bg-neutral-100 dark:hover:bg-neutral-700',
          active ? 'bg-neutral-100 dark:bg-neutral-700 font-medium' : ''
        )}
      >
        {icon && <div className="mr-2">{icon}</div>}
        <span className="flex-grow">{label}</span>
        {shortcut && <span className="text-xs text-gray-500 ml-4">{shortcut}</span>}
      </button>
    </Dropdown.Item>
  )
}

const DropdownDivider: React.FC = () => {
  return <Dropdown.Separator className="h-px my-1 bg-gray-200 dark:bg-neutral-700" />
}

// 字体下拉组件
const FontDropdown = ({ value, onChange }: { value: string, onChange: (fontFamily: string, fontLabel: string) => void }) => {
  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <button className="flex items-center justify-between h-8 px-2 border rounded 
                          bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 
                          gap-1 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 
                          dark:border-neutral-700 w-36 text-left">
          <span className="text-xs truncate" style={{ fontFamily: FONT_FAMILIES.find(f => f.label === value)?.value }}>{value}</span>
          <Icon name="ChevronDown" className="w-3 h-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
        </button>
      </Dropdown.Trigger>
      <Dropdown.Content
        className="z-50 w-52 bg-white dark:bg-neutral-800 rounded-md shadow-lg p-1 border dark:border-neutral-700 max-h-[400px] overflow-y-auto"
      >
        {FONT_FAMILY_GROUPS.map((group: FontGroup) => (
          <div key={group.label} className="mb-1 last:mb-0">
            <div className="px-2 py-1 text-xs text-gray-500 dark:text-gray-400 font-medium">{group.label}</div>
            <div className="flex flex-col gap-0.5">
              {group.options.map((font: FontOption) => (
                <Dropdown.Item
                  key={font.value}
                  className={cn(
                    "flex px-2 py-1.5 text-xs rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 outline-none",
                    font.label === value && "bg-gray-100 dark:bg-neutral-700 font-medium"
                  )}
                  onClick={() => onChange(font.value, font.label)}
                >
                  <span style={{ fontFamily: font.value }}>{font.label}</span>
                </Dropdown.Item>
              ))}
            </div>
          </div>
        ))}
      </Dropdown.Content>
    </Dropdown.Root>
  )
}

// 字体大小下拉组件
const FontSizeDropdown = ({ value, onChange }: { value: string, onChange: (fontSize: string) => void }) => {
  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <button className="flex items-center h-8 px-2 border rounded bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 gap-1 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 dark:border-neutral-700">
          <span className="min-w-[24px] cursor-default text-sm">{getDisplayLabel(value)}</span>
          <Icon name="ChevronDown" className="w-3 h-3 text-gray-500 dark:text-gray-400" />
        </button>
      </Dropdown.Trigger>
      <Dropdown.Content
        className="z-50 min-w-[65px] bg-white dark:bg-neutral-800 rounded-md shadow-lg p-1 border dark:border-neutral-700 max-h-[300px] overflow-y-auto"
      >
        <div className="flex flex-col gap-0.5">
          {fontSizeOptions.map((fontSize) => (
            <Dropdown.Item
              key={fontSize.value}
              className={cn(
                "flex px-2 py-1.5 text-sm rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 outline-none",
                fontSize.value === value && "bg-gray-100 dark:bg-neutral-700 font-medium"
              )}
              onClick={() => onChange(fontSize.value)}
            >
              {fontSize.label}
            </Dropdown.Item>
          ))}
        </div>
      </Dropdown.Content>
    </Dropdown.Root>
  )
}

// 开始标签的工具栏
const StartTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  if (!editor) return null;
  
  // 追踪格式刷是否激活
  const [formatPainterActive, setFormatPainterActive] = useState(false);
  // 字体选项
  const [currentFont, setCurrentFont] = useState(DEFAULT_FONT.label);
  // 字体大小
  const [currentFontSize, setCurrentFontSize] = useState('16px');
  // 添加一个状态变量来强制更新组件，确保激活状态正确
  const [forceUpdate, setForceUpdate] = useState(0);
  // 引入评论存储
  const { addComment } = useCommentStore();
  
  // 当组件挂载时，注册格式刷状态变化回调
  useEffect(() => {
    if (editor) {
      // 为格式刷扩展注册状态变化回调
      if (editor.storage.formatPainter) {
        editor.storage.formatPainter.onActiveChange = (isActive: boolean) => {
          setFormatPainterActive(isActive);
        };
      }
      
      // 监听选区变化，更新当前字体和字体大小
      const updateTextStyles = () => {
        // 更新字体
        const fontFamily = editor.getAttributes('textStyle').fontFamily;
        if (fontFamily) {
          const font = FONT_FAMILIES.find(f => f.value === fontFamily);
          if (font) {
            setCurrentFont(font.label);
          }
        } else {
          setCurrentFont(DEFAULT_FONT.label); // 默认字体
        }
        
        // 更新字体大小
        const fontSize = editor.getAttributes('textStyle').fontSize;
        setCurrentFontSize(fontSize || '16px'); // 默认16px
        
        // 强制更新组件以确保激活状态正确
        setForceUpdate(prev => prev + 1);
      };
      
      // 监听选区变化和编辑器内容更新
      editor.on('selectionUpdate', updateTextStyles);
      editor.on('transaction', updateTextStyles);
      editor.on('update', updateTextStyles);
      
      // 初始化时执行一次更新
      updateTextStyles();
      
      return () => {
        // 清理回调
        if (editor.storage.formatPainter) {
          editor.storage.formatPainter.onActiveChange = null;
        }
        editor.off('selectionUpdate', updateTextStyles);
        editor.off('transaction', updateTextStyles);
        editor.off('update', updateTextStyles);
      };
    }
  }, [editor]);
  
  // 格式刷功能
  const toggleFormatPainter = () => {
    if (editor) {
      editor.chain().focus().toggleFormatPainter().run();
    }
  };
  
  // 字体改变处理函数
  const handleFontChange = (fontFamily: string, fontLabel: string) => {
    if (!editor) return;
    
    if (fontFamily) {
      editor.chain().focus().setFontFamily(fontFamily).run();
      setCurrentFont(fontLabel);
    } else {
      editor.chain().focus().unsetFontFamily().run();
      setCurrentFont(DEFAULT_FONT.label);
    }
  };
  
  // 字体大小改变处理函数
  const handleFontSizeChange = (fontSize: string) => {
    if (!editor) return;
    
    if (fontSize) {
      editor.chain().focus().setFontSize(fontSize).run();
      setCurrentFontSize(fontSize);
    } else {
      editor.chain().focus().unsetFontSize().run();
      setCurrentFontSize('');
    }
  };
  
  // 增大字体大小
  const increaseFontSize = () => {
    if (!editor) return;
    const nextSize = getNextLargerFontSize(currentFontSize);
    editor.chain().focus().setFontSize(nextSize).run();
    setCurrentFontSize(nextSize);
  };
  
  // 减小字体大小
  const decreaseFontSize = () => {
    if (!editor) return;
    const nextSize = getNextSmallerFontSize(currentFontSize);
    editor.chain().focus().setFontSize(nextSize).run();
    setCurrentFontSize(nextSize);
  };
  
  // 添加批注函数
  const handleAddComment = useCallback(() => {
    if (!editor) return;
    
    // 获取选中文本
    const { from, to } = editor.state.selection;
    
    // 如果没有选中文本，不添加批注
    if (from === to) return;
    
    // 获取选中的文本内容
    const selectedText = editor.state.doc.textBetween(from, to);
    
    // 生成批注ID
    const commentId = generateUniqueId();
    
    // 给选中的文本添加批注标记
    editor.chain().focus().setComment(commentId).run();
    
    // 存储批注信息到存储
    addComment({
      id: commentId,
      anchor: { from, to },
      original: selectedText,
      suggestions: [],
      state: 'active',
    });
  }, [editor, addComment]);
  
  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          onClick={() => editor.commands.undo()}
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="撤销 (Ctrl+Z)"
        >
          <Icon name="Undo" className="w-4 h-4" />
        </button>
        
        <button 
          onClick={() => editor.commands.redo()}
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="重做 (Ctrl+Y)"
        >
          <Icon name="Redo" className="w-4 h-4" />
        </button>
        
        {/* 批注按钮 */}
        <button 
          onClick={handleAddComment}
          className={cn(
            'p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 focus:outline-none focus:ring-1 focus:ring-[#106ebe]',
            editor.isActive('comment') ? 'bg-[#edebe9] dark:bg-neutral-700 text-[#106ebe] dark:text-blue-400' : 'text-neutral-700 dark:text-neutral-300'
          )}
          title="添加批注"
          disabled={editor.state.selection.empty || editor.isActive('comment')}
        >
          <Icon name="MessageSquare" className="w-4 h-4" />
        </button>
        
        <button 
          onClick={toggleFormatPainter}
          className={cn(
            'p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 focus:outline-none focus:ring-1 focus:ring-[#106ebe]',
            formatPainterActive ? 'bg-[#edebe9] dark:bg-neutral-700 text-[#106ebe] dark:text-blue-400' : 'text-neutral-700 dark:text-neutral-300'
          )}
          title="格式刷"
        >
          <img src="/assets/icons/format-painter.svg" alt="格式刷" className="w-4 h-4" />
        </button>
        
        <button 
          onClick={() => editor.chain().focus().unsetAllMarks().clearNodes().run()}
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="清除格式"
        >
          <img src="/assets/icons/clear-format.svg" alt="清除格式" className="w-4 h-4" />
        </button>
      </div>
      
      {/* 字体组 */}
      <div className="flex flex-col items-center mx-1">
        <div className="flex items-center">
          <Dropdown.Root>
            <Dropdown.Trigger asChild>
              <button className="flex items-center justify-between h-8 px-2 border rounded 
                                bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 
                                gap-1 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 
                                dark:border-neutral-700 w-36 text-left">
                <span className="text-xs truncate" style={{ fontFamily: FONT_FAMILIES.find(f => f.label === currentFont)?.value }}>{currentFont}</span>
                <Icon name="ChevronDown" className="w-3 h-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
              </button>
            </Dropdown.Trigger>
            <Dropdown.Content
              className="z-50 w-52 bg-white dark:bg-neutral-800 rounded-md shadow-lg p-1 border dark:border-neutral-700 max-h-[400px] overflow-y-auto"
            >
              {FONT_FAMILY_GROUPS.map((group) => (
                <div key={group.label} className="mb-1 last:mb-0">
                  <div className="px-2 py-1 text-xs text-gray-500 dark:text-gray-400 font-medium">{group.label}</div>
                  <div className="flex flex-col gap-0.5">
                    {group.options.map((font) => (
                      <Dropdown.Item
                        key={font.value}
                        className={cn(
                          "flex px-2 py-1.5 text-xs rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 outline-none",
                          font.label === currentFont && "bg-gray-100 dark:bg-neutral-700 font-medium"
                        )}
                        onClick={() => handleFontChange(font.value, font.label)}
                      >
                        <span style={{ fontFamily: font.value }}>{font.label}</span>
                      </Dropdown.Item>
                    ))}
                  </div>
                </div>
              ))}
            </Dropdown.Content>
          </Dropdown.Root>
          
          <Dropdown.Root>
            <Dropdown.Trigger asChild>
              <button className="flex items-center h-8 px-2 border rounded bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 gap-1 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 dark:border-neutral-700 ml-1">
                <span className="min-w-[24px] cursor-default text-sm">{getDisplayLabel(currentFontSize)}</span>
                <Icon name="ChevronDown" className="w-3 h-3 text-gray-500 dark:text-gray-400" />
              </button>
            </Dropdown.Trigger>
            <Dropdown.Content
              className="z-50 min-w-[65px] bg-white dark:bg-neutral-800 rounded-md shadow-lg p-1 border dark:border-neutral-700 max-h-[300px] overflow-y-auto"
            >
              <div className="flex flex-col gap-0.5">
                {fontSizeOptions.map((fontSize) => (
                  <Dropdown.Item
                    key={fontSize.value}
                    className={cn(
                      "flex px-2 py-1.5 text-sm rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 outline-none",
                      fontSize.value === currentFontSize && "bg-gray-100 dark:bg-neutral-700 font-medium"
                    )}
                    onClick={() => handleFontSizeChange(fontSize.value)}
                  >
                    {fontSize.label}
                  </Dropdown.Item>
                ))}
              </div>
            </Dropdown.Content>
          </Dropdown.Root>
          
          <button
            onClick={increaseFontSize}
            title="增大字号"
            className="flex items-center justify-center h-8 w-8 rounded border bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-700 ml-1"
          >
            <Icon name="ChevronUp" className="w-4 h-4" />
          </button>
          <button
            onClick={decreaseFontSize}
            title="减小字号"
            className="flex items-center justify-center h-8 w-8 rounded border bg-white hover:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-neutral-700 ml-1"
          >
            <Icon name="ChevronDown" className="w-4 h-4" />
          </button>
        </div>
        <div className="text-xs text-gray-600 mt-1">字体</div>
      </div>
      
      {/* 段落组 */}
      <div className="flex flex-col items-center mx-1">
        <div className="flex items-center">
          <ButtonGroup>
            <Button
              icon={<Icon name="AlignLeft" className="h-5 w-5" />}
              tooltip="左对齐"
              active={editor.isActive({ textAlign: 'left' })}
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
            />
            <Button
              icon={<Icon name="AlignCenter" className="h-5 w-5" />}
              tooltip="居中对齐"
              active={editor.isActive({ textAlign: 'center' })}
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
            />
            <Button
              icon={<Icon name="AlignRight" className="h-5 w-5" />}
              tooltip="右对齐"
              active={editor.isActive({ textAlign: 'right' })}
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
            />
            <Button
              icon={<Icon name="AlignJustify" className="h-5 w-5" />}
              tooltip="两端对齐"
              active={editor.isActive({ textAlign: 'justify' })}
              onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            />
          </ButtonGroup>
          <ButtonWithDropdown
            icon={<Icon name="LineHeight" className="h-5 w-5" />}
            tooltip="行距"
            className="ml-1"
          >
            <DropdownItem 
              label="单倍行距"
              onClick={() => editor.chain().focus().setLineHeight('normal').run()}
              active={editor.isActive({ lineHeight: 'normal' })}
            />
            <DropdownItem 
              label="1.15"
              onClick={() => editor.chain().focus().setLineHeight('1.15').run()}
              active={editor.isActive({ lineHeight: '1.15' })}
            />
            <DropdownItem 
              label="1.5"
              onClick={() => editor.chain().focus().setLineHeight('1.5').run()}
              active={editor.isActive({ lineHeight: '1.5' })}
            />
            <DropdownItem 
              label="2.0"
              onClick={() => editor.chain().focus().setLineHeight('2.0').run()}
              active={editor.isActive({ lineHeight: '2.0' })}
            />
          </ButtonWithDropdown>
        </div>
        <div className="text-xs text-gray-600 mt-1">段落</div>
      </div>
      
      {/* 样式组 */}
      <div className="flex flex-col items-center mx-1">
        <div className="flex items-center">
          <ButtonGroup>
            <Button
              icon={<Icon name="Bold" className="h-5 w-5" />}
              tooltip="粗体"
              tooltipShortcut="Ctrl+B"
              active={editor.isActive('bold')}
              onClick={() => editor.chain().focus().toggleBold().run()}
            />
            <Button
              icon={<Icon name="Italic" className="h-5 w-5" />}
              tooltip="斜体"
              tooltipShortcut="Ctrl+I"
              active={editor.isActive('italic')}
              onClick={() => editor.chain().focus().toggleItalic().run()}
            />
            <Button
              icon={<Icon name="Underline" className="h-5 w-5" />}
              tooltip="下划线"
              tooltipShortcut="Ctrl+U"
              active={editor.isActive('underline')}
              onClick={() => editor.chain().focus().toggleUnderline().run()}
            />
            <ButtonWithDropdown
              icon={<Icon name="MoreHorizontal" className="h-5 w-5" />}
              tooltip="更多格式"
            >
              <DropdownItem 
                icon={<Icon name="Strikethrough" className="h-4 w-4" />}
                label="删除线"
                onClick={() => editor.chain().focus().toggleStrike().run()}
                active={editor.isActive('strike')}
                shortcut="Alt+Shift+5"
              />
              <DropdownItem 
                icon={<Icon name="Superscript" className="h-4 w-4" />}
                label="上标"
                onClick={() => editor.chain().focus().toggleSuperscript().run()}
                active={editor.isActive('superscript')}
                shortcut="Ctrl+Shift++"
              />
              <DropdownItem 
                icon={<Icon name="Subscript" className="h-4 w-4" />}
                label="下标"
                onClick={() => editor.chain().focus().toggleSubscript().run()}
                active={editor.isActive('subscript')}
                shortcut="Ctrl+="
              />
              <DropdownDivider />
              <DropdownItem 
                icon={<Icon name="Highlighter" className="h-4 w-4" />}
                label="文本高亮"
                onClick={() => editor.chain().focus().toggleHighlight().run()}
                active={editor.isActive('highlight')}
              />
            </ButtonWithDropdown>
          </ButtonGroup>
        </div>
        <div className="text-xs text-gray-600 mt-1">字体样式</div>
      </div>
      
      {/* 编辑组 */}
      <div className="flex flex-col items-center mx-1">
        <div className="flex items-center">
          <Button
            icon={<Icon name="Search" className="h-5 w-5" />}
            label="查找"
            tooltip="查找"
            onClick={() => console.log('查找')}
          />
          <Button
            icon={<Icon name="Replace" className="h-5 w-5" />}
            label="替换"
            tooltip="替换"
            onClick={() => console.log('替换')}
            className="ml-1"
          />
        </div>
        <div className="text-xs text-gray-600 mt-1">编辑</div>
      </div>
    </div>
  );
}

// 插入标签的工具栏
const InsertTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !editor) return;
    
    try {
      // 获取文件扩展名
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      
      // 使用FileReader读取文件内容
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        if (fileExt === 'docx') {
          // 使用mammoth将docx转换为html
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const result = await mammoth.convertToHtml({arrayBuffer});
          const html = result.value;
          
          // 将HTML插入到编辑器中
          editor.commands.insertContent(html);
        } 
        else if (fileExt === 'txt') {
          // 直接读取文本内容
          const text = e.target?.result as string;
          // 将文本内容转换为适合编辑器的格式（简单的p标签包装）
          const formattedText = text.split('\n')
            .map(line => line.trim() ? `<p>${line}</p>` : '<p><br></p>')
            .join('');
          
          // 插入内容
          editor.commands.insertContent(formattedText);
        }
        else if (fileExt === 'html') {
          // 直接插入HTML内容
          const html = e.target?.result as string;
          editor.commands.insertContent(html);
        }
      };
      
      if (fileExt === 'docx') {
        reader.readAsArrayBuffer(file);
      } else {
        reader.readAsText(file);
      }
    } catch (error) {
      console.error('导入文档失败：', error);
      alert('导入文档失败，请重试');
    } finally {
      // 重置文件输入，允许上传相同的文件
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  
  const handleImportClick = () => {
    fileInputRef.current?.click();
  };
  
  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="图片"
          onClick={() => editor?.chain().focus().setImageBlock().run()}
        >
          <Icon name="Image" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="导入文档"
          onClick={handleImportClick}
        >
          <Icon name="FileText" className="w-4 h-4" />
        </button>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".docx,.txt,.html,.htm"
          onChange={handleFileUpload}
          className="hidden"
        />
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入表格"
          onClick={() => editor?.commands.insertTable({ rows: 3, cols: 3, withHeaderRow: true })}
        >
          <Icon name="Table" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="引用"
          onClick={() => editor?.chain().focus().setBlockquote().run()}
        >
          <Icon name="Quote" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入公式"
        >
          <Icon name="Sigma" className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex items-center gap-px">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入二维码"
        >
          <Icon name="QrCode" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入代码块"
        >
          <Icon name="FileCode" className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// 表格标签的工具栏
const TableTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="表格属性"
        >
          <Icon name="TableProperties" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入行"
        >
          <Icon name="ArrowDown" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="插入列"
        >
          <Icon name="ArrowRight" className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex items-center gap-px">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="表格样式"
        >
          <Icon name="LayoutGrid" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="表格布局"
        >
          <Icon name="Table2" className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// 工具标签的工具栏
const ToolsTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  // 添加状态来跟踪是否显示换行符
  const [showInvisibles, setShowInvisibles] = useState(false);

  // 更新状态的处理函数
  const updateInvisibleState = useCallback(() => {
    if (editor && editor.storage.invisibleCharacters) {
      setShowInvisibles(editor.storage.invisibleCharacters.isVisible);
    }
  }, [editor]);

  // 同步编辑器的invisible状态
  useEffect(() => {
    if (editor) {
      // 初始同步
      updateInvisibleState();
      
      // 监听编辑器的事务和更新事件
      editor.on('transaction', updateInvisibleState);
      editor.on('update', updateInvisibleState);
      
      return () => {
        // 组件卸载时清理事件监听
        editor.off('transaction', updateInvisibleState);
        editor.off('update', updateInvisibleState);
      };
    }
  }, [editor, updateInvisibleState]);

  // 切换显示/隐藏换行符
  const handleToggleInvisibles = () => {
    if (editor) {
      try {
        // 直接执行命令，不使用can()检查
        editor.chain().focus().toggleInvisibleCharacters().run();
        
        // 在下一个事件循环更新状态，确保与编辑器状态同步
        setTimeout(() => {
          if (editor.storage.invisibleCharacters) {
            setShowInvisibles(editor.storage.invisibleCharacters.isVisible);
          }
        }, 10);
      } catch (error) {
        console.error('切换换行符显示出错:', error);
      }
    }
  };

  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="分栏"
        >
          <Icon name="Columns2" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="分行"
        >
          <Icon name="Rows2" className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className={cn(
            "p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 focus:outline-none focus:ring-1 focus:ring-[#106ebe]",
            showInvisibles 
              ? "bg-[#edebe9] dark:bg-neutral-700 text-blue-500 dark:text-blue-400" 
              : "text-neutral-700 dark:text-neutral-300"
          )}
          title={showInvisibles ? "隐藏换行符" : "显示换行符"}
          onClick={handleToggleInvisibles}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="16" 
            height="16" 
            viewBox="0 0 16 16" 
            fill="none" 
            className="w-4 h-4"
          >
            <path 
              d="M3 2h10M3 8h5.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M10 5v6" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M10 11l-1.5-1.5M10 11l1.5-1.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
            <path 
              d="M3 14h10" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
      
      <div className="flex items-center gap-px">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="选项"
        >
          <Icon name="CircleDot" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="比较"
        >
          <Icon name="Equal" className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// 页面标签的工具栏
const PageTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="页面布局"
        >
          <Icon name="PanelLeft" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="页面对齐"
        >
          <Icon name="AlignVerticalJustifyCenter" className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex items-center gap-px">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="页面列数"
        >
          <Icon name="Columns2" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="页面行数"
        >
          <Icon name="Rows2" className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// 导出标签的工具栏
const ExportTabToolbar: React.FC<{editor?: Editor}> = ({ editor }) => {
  return (
    <div className="flex items-center w-full">
      <div className="flex items-center gap-px border-r border-[#e1dfdd] dark:border-neutral-700 pr-3 mr-3">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="下载文档"
        >
          <Icon name="Download" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="打印"
        >
          <Icon name="Printer" className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex items-center gap-px">
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="分享"
        >
          <Icon name="Share" className="w-4 h-4" />
        </button>
        
        <button 
          className="p-1.5 rounded hover:bg-[#f3f2f1] dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 focus:outline-none focus:ring-1 focus:ring-[#106ebe]"
          title="保存"
        >
          <Icon name="Save" className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
} 