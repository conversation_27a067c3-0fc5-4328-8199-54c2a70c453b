import React, { FC, ReactNode, useState } from 'react'
import { Comment } from '@/lib/stores/commentStore'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/Button'
import { Icon } from '@/components/ui/Icon'

interface CommentItemProps {
  comment: Comment
  isActive: boolean
  onActivate: () => void
  onStateChange: (id: string, state: 'active' | 'hidden' | 'resolved') => void
  onDelete: () => void
  onTextClick: () => void
  calculateDiff: (original: string, suggested: string) => ReactNode
  onApplyRevision: (commentId: string, text: string) => void
  newSuggestion: string
  onSuggestionChange?: (value: string) => void
  onAddSuggestion: () => void
  onUpdateContent?: (content: string) => void
  index?: number
}

export const CommentItem: FC<CommentItemProps> = ({
  comment,
  isActive,
  onActivate,
  onStateChange,
  onDelete,
  onTextClick,
  calculateDiff,
  onApplyRevision,
  newSuggestion,
  onSuggestionChange,
  onAddSuggestion,
  onUpdateContent,
  index = 0,
}) => {
  const [menuOpen, setMenuOpen] = useState(false)
  const [showRevision, setShowRevision] = useState(false)
  
  return (
    <div 
      className={cn(
        "relative border rounded-md overflow-hidden transition-all mb-3",
        isActive 
          ? "border-blue-500 shadow-md" 
          : "border-gray-200 hover:border-gray-300"
      )}
      onClick={() => !isActive && onActivate()}
    >
      {/* 头部信息 */}
      <div className="bg-neutral-50 p-2 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 rounded-full bg-red-600 flex items-center justify-center text-xs text-white font-semibold">
            {/* 批注序号 */}
            {index || 1}
          </div>
          <span className="text-sm font-medium">批注</span>
        </div>
        
        {/* 右上角操作菜单 */}
        <div className="flex items-center gap-1">
          <Button 
            variant="ghost" 
            className="h-7 w-7 p-0" 
            onClick={(e) => {
              e.stopPropagation()
              setMenuOpen(!menuOpen)
            }}
          >
            <Icon name="Menu" className="h-4 w-4" />
          </Button>
          
          {menuOpen && (
            <div className="absolute right-8 mt-8 bg-white shadow-md rounded-md border border-gray-200 py-1 z-50">
              <button
                className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                  setMenuOpen(false)
                }}
              >
                <Icon name="Trash2" className="h-4 w-4 text-red-500" />
                <span>删除</span>
              </button>
              <button
                className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation()
                  onStateChange(comment.id, comment.state === 'hidden' ? 'active' : 'hidden')
                  setMenuOpen(false)
                }}
              >
                <Icon name={comment.state === 'hidden' ? 'Eye' : 'Eye'} className="h-4 w-4" />
                <span>{comment.state === 'hidden' ? '显示' : '隐藏'}</span>
              </button>
              <button
                className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation()
                  onStateChange(comment.id, 'resolved')
                  setMenuOpen(false)
                }}
              >
                <Icon name="MessageSquare" className="h-4 w-4 text-green-500" />
                <span>标记为已解决</span>
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* 批注内容框 */}
      <div className="p-3 bg-white">
        <div className="mb-2">
          <div 
            className="p-2 bg-[#FFF3CD] rounded cursor-pointer text-sm font-仿宋体 mb-2"
            onClick={onTextClick}
          >
            {comment.original}
          </div>
          
          {isActive ? (
            <textarea
              className="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-微软雅黑 resize-none"
              placeholder="在此处输入批注内容..."
              rows={2}
              value={comment.content || ""}
              onChange={(e) => {
                onUpdateContent?.(e.target.value);
              }}
            />
          ) : (
            <div className="text-sm font-微软雅黑 text-gray-700 p-2">
              {comment.content || "无批注内容"}
            </div>
          )}
        </div>
      </div>
      
      {/* 修订内容部分 */}
      <div className="border-t border-gray-200">
        <div 
          className="p-2 bg-gray-50 flex justify-between items-center cursor-pointer"
          onClick={() => setShowRevision(!showRevision)}
        >
          <span className="text-sm font-medium">修订内容</span>
          <Icon name={showRevision ? "ChevronUp" : "ChevronDown"} className="h-4 w-4" />
        </div>
        
        {showRevision && (
          <div className="p-3 bg-white">
            {/* 已有修订建议 */}
            {comment.suggestions.length > 0 && (
              <div className="mb-3">
                <h4 className="text-sm font-medium mb-2">修订建议:</h4>
                
                <div className="space-y-2">
                  {comment.suggestions.map(suggestion => (
                    <div key={suggestion.id} className="bg-gray-50 p-2 rounded-md text-sm">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500">
                          {new Date(suggestion.createdAt).toLocaleString()}
                        </span>
                        <Button
                          variant="ghost"
                          className="h-6 text-xs text-blue-600 hover:text-blue-800"
                          onClick={() => onApplyRevision(comment.id, suggestion.text)}
                        >
                          应用
                        </Button>
                      </div>
                      
                      <div className="p-2 bg-white border border-gray-200 rounded-md">
                        {calculateDiff(comment.original, suggestion.text)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* 添加修订建议 */}
            {isActive && (
              <div className="space-y-2">
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-微软雅黑 resize-none"
                  placeholder="添加修订建议..."
                  rows={3}
                  value={newSuggestion}
                  onChange={(e) => onSuggestionChange?.(e.target.value)}
                />
                
                <div className="flex justify-end items-center">
                  <Button
                    variant="ghost" 
                    className="h-7 text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700"
                    onClick={onAddSuggestion}
                    disabled={!newSuggestion.trim()}
                  >
                    提交修订
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* 底部操作按钮 */}
      <div className="flex justify-between items-center p-2 border-t border-gray-200 bg-gray-50">
        <div className="flex space-x-1">
          <Button
            variant="ghost"
            className="h-7 text-xs"
            onClick={() => {
              // 编辑批注内容 - 激活
              onActivate()
            }}
          >
            编辑
          </Button>
          <div className="border-r border-gray-300 mx-1"></div>
          <Button
            variant="ghost"
            className="h-7 text-xs"
            onClick={() => onStateChange(comment.id, 'hidden')}
          >
            隐藏本批注
          </Button>
          <div className="border-r border-gray-300 mx-1"></div>
          <Button
            variant="ghost" 
            className="h-7 text-xs"
            onClick={() => setShowRevision(!showRevision)}
          >
            {showRevision ? "关闭修订" : "修订"}
          </Button>
        </div>
      </div>
    </div>
  )
} 