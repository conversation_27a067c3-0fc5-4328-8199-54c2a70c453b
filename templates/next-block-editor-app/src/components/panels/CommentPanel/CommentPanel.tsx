import React, { FC, useCallback, useEffect, useState } from 'react'
import { Editor } from '@tiptap/react'
import { useCommentStore, Comment, CommentState } from '@/lib/stores/commentStore'
import { CommentItem } from './CommentItem'
import { Icon } from '@/components/ui/Icon'
import { Button } from '@/components/ui/Button'
import { cn } from '@/lib/utils'

// 差异化算法
import * as DiffMatchPatch from 'diff-match-patch'

interface CommentPanelProps {
  editor: Editor
  isOpen?: boolean
  onClose?: () => void
}

export const CommentPanel: FC<CommentPanelProps> = ({
  editor,
  isOpen = false,
  onClose,
}) => {
  const {
    comments,
    activeCommentId,
    setActiveComment,
    deleteComment,
    setCommentState,
    addSuggestion,
    updateComment,
  } = useCommentStore()
  
  const [newSuggestion, setNewSuggestion] = useState<string>('')
  
  // 处理批注状态变更
  const handleStateChange = useCallback((id: string, state: CommentState) => {
    setCommentState(id, state)
  }, [setCommentState])
  
  // 处理删除批注
  const handleDeleteComment = useCallback((id: string) => {
    // 移除文档中的标记
    if (editor && id) {
      editor.commands.command(({ tr, dispatch }) => {
        if (dispatch) {
          const comment = comments.find(c => c.id === id)
          if (comment) {
            tr.removeMark(comment.anchor.from, comment.anchor.to, editor.schema.marks.comment)
            dispatch(tr)
          }
        }
        return true
      })
    }
    
    // 从存储中删除批注
    deleteComment(id)
  }, [editor, comments, deleteComment])
  
  // 添加修订建议
  const handleAddSuggestion = useCallback((commentId: string) => {
    if (newSuggestion.trim()) {
      addSuggestion(commentId, newSuggestion)
      setNewSuggestion('')
    }
  }, [newSuggestion, addSuggestion])
  
  // 处理点击批注内容，让编辑器定位到批注位置
  const handleCommentClick = useCallback((comment: Comment) => {
    if (editor) {
      editor.commands.setTextSelection({
        from: comment.anchor.from,
        to: comment.anchor.to,
      })
      editor.commands.scrollIntoView()
    }
  }, [editor])
  
  // 根据修订建议更新文档内容
  const applyRevision = useCallback((commentId: string, newText: string) => {
    const comment = comments.find(c => c.id === commentId)
    if (!comment || !editor) return
    
    editor.commands.command(({ tr, dispatch }) => {
      if (dispatch) {
        // 替换文本内容
        tr.replaceWith(comment.anchor.from, comment.anchor.to, editor.schema.text(newText))
        
        // 重新应用批注标记，确保新文本也有高亮和下划线样式
        const commentMark = editor.schema.marks.comment.create({ id: commentId })
        tr.addMark(comment.anchor.from, comment.anchor.from + newText.length, commentMark)
        
        dispatch(tr)
      }
      return true
    })
    
    // 更新批注中的原始文本
    updateComment(commentId, {
      original: newText,
    })
  }, [editor, comments, updateComment])
  
  // 计算差异
  const calculateDiff = useCallback((original: string, suggested: string) => {
    const dmp = new DiffMatchPatch.diff_match_patch()
    const diffs = dmp.diff_main(original, suggested)
    dmp.diff_cleanupSemantic(diffs)
    
    return diffs.map((diff, index) => {
      const [type, text] = diff
      
      if (type === 0) { // 相同
        return <span key={index}>{text}</span>
      } else if (type === -1) { // 删除
        return <span key={index} className="line-through text-red-600">{text}</span>
      } else { // 新增
        return <span key={index} className="underline text-green-600">{text}</span>
      }
    })
  }, [])
  
  // 处理更新批注内容
  const handleUpdateContent = useCallback((id: string, content: string) => {
    updateComment(id, { content });
  }, [updateComment]);
  
  return (
    <aside 
      className={cn(
        "fixed top-24 right-4 h-[calc(100vh-144px)] bg-white shadow-lg w-[300px] border border-gray-200 z-50 flex flex-col rounded-md overflow-hidden",
        isOpen ? "opacity-100" : "opacity-0 pointer-events-none",
        "transition-opacity duration-300 ease-in-out"
      )}
    >
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-white">
        <h3 className="text-lg font-medium">批注</h3>
        <Button variant="ghost" buttonSize="icon" onClick={onClose}>
          <Icon name="X" className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {comments.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <Icon name="MessageSquare" className="h-8 w-8 mx-auto mb-2" />
            <p>没有批注</p>
            <p className="text-sm">选择文本并添加批注</p>
          </div>
        ) : (
          <div className="space-y-4">
            {comments.map((comment, index) => (
              <CommentItem
                key={comment.id}
                comment={comment}
                isActive={comment.id === activeCommentId}
                onActivate={() => setActiveComment(comment.id)}
                onStateChange={handleStateChange}
                onDelete={() => handleDeleteComment(comment.id)}
                onTextClick={() => handleCommentClick(comment)}
                calculateDiff={calculateDiff}
                onApplyRevision={applyRevision}
                newSuggestion={activeCommentId === comment.id ? newSuggestion : ''}
                onSuggestionChange={activeCommentId === comment.id ? setNewSuggestion : undefined}
                onAddSuggestion={() => handleAddSuggestion(comment.id)}
                onUpdateContent={(content) => handleUpdateContent(comment.id, content)}
                index={index + 1}
              />
            ))}
          </div>
        )}
      </div>
    </aside>
  )
} 