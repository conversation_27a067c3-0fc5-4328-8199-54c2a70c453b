import React, { FC, useCallback, useEffect, useState } from 'react'
import { Editor } from '@tiptap/react'
import { BubbleMenu } from '@tiptap/react'
import { Button } from '@/components/ui/Button'
import { Icon } from '@/components/ui/Icon'
import { useCommentStore } from '@/lib/stores/commentStore'
import { generateUniqueId } from '@/lib/utils'

interface CommentMenuProps {
  editor: Editor
}

export const CommentMenu: FC<CommentMenuProps> = ({ editor }) => {
  const { addComment } = useCommentStore()
  
  const handleAddComment = useCallback(() => {
    // 获取选中文本
    const { from, to } = editor.state.selection
    
    // 如果没有选中文本，不添加批注
    if (from === to) return
    
    // 获取选中的文本内容
    const selectedText = editor.state.doc.textBetween(from, to)
    
    // 生成批注ID
    const commentId = generateUniqueId()
    
    // 给选中的文本添加批注标记
    editor.chain().setComment(commentId).run()
    
    // 存储批注信息到存储
    addComment({
      id: commentId,
      anchor: { from, to },
      original: selectedText,
      suggestions: [],
      state: 'active',
    })
  }, [editor, addComment])
  
  return (
    <BubbleMenu
      editor={editor}
      tippyOptions={{
        duration: 100,
        arrow: false,
        placement: 'bottom',
      }}
      shouldShow={({ editor, from, to }) => {
        // 只在有文本选择且没有批注标记时显示
        return (
          from !== to &&
          !editor.isActive('comment')
        )
      }}
    >
      {/* <div className="flex bg-white border border-gray-200 shadow-md rounded-md p-1 items-center">
        <Button
          variant="ghost"
          className="p-1 h-8 flex items-center gap-1 text-sm"
          onClick={handleAddComment}
        >
          <Icon name="MessageSquare" className="h-4 w-4" />
          <span>添加批注</span>
        </Button>
      </div> */}
    </BubbleMenu>
  )
} 