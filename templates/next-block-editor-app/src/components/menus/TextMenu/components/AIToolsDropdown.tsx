import { Icon } from '@/components/ui/Icon'
import { Toolbar } from '@/components/ui/Toolbar'
import { DropdownButton } from '@/components/ui/Dropdown'
import { Surface } from '@/components/ui/Surface'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import { useCallback, useState } from 'react'
import { Editor } from '@tiptap/react'
import { AIToolType } from '@/extensions/AITools/AITools'
import { XAgentService, ModelConfigManager } from '@/components/AI/services/XAgentService'
import { AI_TOOL_PROMPTS } from '@/extensions/AITools/AITools'
import { applyTypingEffect } from '@/lib/api/typingEffectHelper'

// 默认测试文本
const DEFAULT_TEST_TEXTS = {
  rewrite: "这是一段测试文本，用于演示AI改写功能。通过AI技术，可以用不同的表达方式呈现相同的内容，使文章更加丰富多样。",
  expand: "人工智能正在改变我们的生活和工作方式。现代AI系统可以理解自然语言，分析图像和视频，甚至创作内容。",
  summarize: "人工智能（AI）在过去几十年中取得了巨大进步。从最初的专家系统到现在的深度学习，AI的能力不断提升。现代AI系统可以识别图像、理解自然语言、玩复杂游戏，甚至创作内容。在医疗领域，AI辅助诊断系统帮助医生更准确地检测疾病。在金融行业，AI用于风险评估和欺诈检测。在日常生活中，我们使用语音助手、推荐系统和智能家居设备。尽管AI带来许多好处，但也引发了关于隐私、就业和偏见等问题的讨论。研究人员正致力于开发更透明、公平和可靠的AI系统。"
};

export interface AIToolsDropdownProps {
  editor: Editor
}

export const AIToolsDropdown = ({ editor }: AIToolsDropdownProps) => {
  const [isProcessing, setIsProcessing] = useState(false);

  // 处理AI工具选择
  const handleSelectTool = useCallback((toolType: AIToolType) => {
    if (!editor || isProcessing) return;
    
    // 获取选中文本
    const { from, to } = editor.state.selection;
    
    // 如果有选中文本，直接处理选中文本
    if (from !== to) {
      setIsProcessing(true);
      
      // 调用AITools扩展的命令
      editor.commands.applyAITool(toolType, () => {
        // 处理完成后重置状态
        setIsProcessing(false);
      });
    } else {
      // 如果没有选中文本，使用默认测试文本但不显示给用户
      const defaultText = DEFAULT_TEST_TEXTS[toolType];
      
      setIsProcessing(true);
      
      // 直接使用XAgentService处理默认文本
      const service = new XAgentService(null, ModelConfigManager.getCurrentConfig());
      // 根据工具类型构建提示词
      const prompt = `${AI_TOOL_PROMPTS[toolType]}\n\n${defaultText}`;
      
      // 发送消息到AI服务处理
      service.sendMessage(prompt, [])
        .then(({ response, error }) => {
          if (error) {
            console.error('AI服务错误:', error);
            setIsProcessing(false);
            return;
          }
          
          // 直接插入处理结果，使用打字效果
          applyTypingEffect(
            editor,
            response,
            50, // 打字速度
            1,  // 每次添加的字符数
            () => {
              setIsProcessing(false);
            }
          );
        })
        .catch(error => {
          console.error('AI处理异常:', error);
          setIsProcessing(false);
        });
    }
  }, [editor, isProcessing]);

  // 不再禁用按钮，即使没有选中文本也可以使用
  const isDisabled = !editor || isProcessing;

  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <Toolbar.Button
          className="text-purple-500 hover:text-purple-600 active:text-purple-600 dark:text-purple-400 dark:hover:text-purple-300 dark:active:text-purple-400"
          activeClassname="text-purple-600 hover:text-purple-600 dark:text-purple-400 dark:hover:text-purple-200"
          disabled={isDisabled}
        >
          {isProcessing ? (
            <>
              <Icon name="Loader" className="animate-spin mr-1" />
              处理中...
            </>
          ) : (
            <>
              <Icon name="Sparkles" className="mr-1" />
              AI工具
              <Icon name="ChevronDown" className="w-2 h-2 ml-1" />
            </>
          )}
        </Toolbar.Button>
      </Dropdown.Trigger>
      <Dropdown.Content asChild>
        <Surface className="p-2 min-w-[10rem]">
          <Dropdown.Item onClick={() => handleSelectTool('rewrite')} disabled={isProcessing}>
            <DropdownButton>
              <Icon name="PenLine" />
              改写
            </DropdownButton>
          </Dropdown.Item>
          <Dropdown.Item onClick={() => handleSelectTool('expand')} disabled={isProcessing}>
            <DropdownButton>
              <Icon name="ArrowRightToLine" />
              扩展
            </DropdownButton>
          </Dropdown.Item>
          <Dropdown.Item onClick={() => handleSelectTool('summarize')} disabled={isProcessing}>
            <DropdownButton>
              <Icon name="ListChecks" />
              总结
            </DropdownButton>
          </Dropdown.Item>
        </Surface>
      </Dropdown.Content>
    </Dropdown.Root>
  )
} 