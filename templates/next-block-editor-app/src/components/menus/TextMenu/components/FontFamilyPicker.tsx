import { DropdownButton, DropdownCategoryTitle } from '@/components/ui/Dropdown'
import { Icon } from '@/components/ui/Icon'
import { Surface } from '@/components/ui/Surface'
import { Toolbar } from '@/components/ui/Toolbar'
import { FONT_FAMILIES, FONT_FAMILY_GROUPS, DEFAULT_FONT } from '@/lib/constants/fonts'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import { useCallback } from 'react'

export type FontFamilyPickerProps = {
  onChange: (value: string) => void // eslint-disable-line no-unused-vars
  value: string
}

export const FontFamilyPicker = ({ onChange, value }: FontFamilyPickerProps) => {
  const currentValue = FONT_FAMILIES.find(font => font.value === value)
  const currentFontLabel = currentValue?.label || DEFAULT_FONT.label

  const selectFont = useCallback((font: { label: string, value: string }) => () => {
    onChange(font.value)
  }, [onChange])

  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <Toolbar.Button active={!!currentValue?.value}>
          {currentFontLabel}
          <Icon name="ChevronDown" className="w-2 h-2" />
        </Toolbar.Button>
      </Dropdown.Trigger>
      <Dropdown.Content
        side="bottom" 
        align="start" 
        alignOffset={0}
        sideOffset={5}
        avoidCollisions={true}
        collisionPadding={10}
        className="z-50 w-48"
      >
        <Surface className="flex flex-col gap-1 px-2 py-4 max-h-[300px] overflow-y-auto">
          {FONT_FAMILY_GROUPS.map(group => (
            <div className="mt-2.5 first:mt-0 gap-0.5 flex flex-col" key={group.label}>
              <DropdownCategoryTitle>{group.label}</DropdownCategoryTitle>
              {group.options.map(font => (
                <DropdownButton
                  isActive={value === font.value}
                  onClick={selectFont(font)}
                  key={`${font.label}_${font.value}`}
                >
                  <span style={{ fontFamily: font.value }}>{font.label}</span>
                </DropdownButton>
              ))}
            </div>
          ))}
        </Surface>
      </Dropdown.Content>
    </Dropdown.Root>
  )
}
