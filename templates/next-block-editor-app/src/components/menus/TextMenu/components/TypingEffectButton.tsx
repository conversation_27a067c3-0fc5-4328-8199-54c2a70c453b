import { Icon } from '@/components/ui/Icon'
import { Toolbar } from '@/components/ui/Toolbar'
import { fetchTextData } from '@/lib/api/mockTextApi'
import { Editor } from '@tiptap/react'
import { useState } from 'react'

interface TypingEffectButtonProps {
  editor: Editor
}

export const TypingEffectButton = ({ editor }: TypingEffectButtonProps) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleGetText = async () => {
    // 基本检查编辑器实例
    if (!editor || !editor.state) {
      return
    }

    try {
      setIsLoading(true)
      
      // 获取当前选区或光标位置
      const { from, to } = editor.state.selection
      
      // 如果有选中文本，使用选中文本；否则使用光标周围的上下文
      let selectedText = ''
      if (from !== to) {
        // 有选中文本
        selectedText = editor.state.doc.textBetween(from, to)
      } else {
        // 没有选中文本，获取当前段落或句子作为上下文
        const paragraph = editor.state.doc.textBetween(
          Math.max(0, from - 100), 
          Math.min(editor.state.doc.content.size, from + 100)
        )
        selectedText = paragraph.trim()
      }
      
      // 确保有内容才调用API
      if (!selectedText) {
        selectedText = "请提供一些补充文本"
      }
      
      // 调用API获取文本数据
      const textData = await fetchTextData(selectedText)
      
      // 获取当前光标位置，确保插入位置正确
      const insertPos = editor.state.selection.to
      
      // 将光标移动到插入位置
      editor.commands.setTextSelection(insertPos)
      
      // 确保先插入一个空格
      editor.commands.insertContent(' ')
      
      // 获得焦点并应用打字效果
      editor.commands.focus()
      
      // 使用扩展命令添加打字效果
      if (textData) {
        editor.commands.addTextWithTypingEffect(textData)
      }
    } catch (error) {
      console.error('获取文本数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <Toolbar.Button
      tooltip="获取补充数据"
      onClick={handleGetText}
      // 只在加载中时禁用按钮
      disabled={isLoading}
    >
      {isLoading ? (
        <Icon name="Loader" className="animate-spin" />
      ) : (
        <Icon name="Download" />
      )}
    </Toolbar.Button>
  )
} 