import { DropdownButton, DropdownCategoryTitle } from '@/components/ui/Dropdown'
import { Icon } from '@/components/ui/Icon'
import { Surface } from '@/components/ui/Surface'
import { Toolbar } from '@/components/ui/Toolbar'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import { useCallback } from 'react'

// Word风格字体大小选项 - 中文字号与西文字号混合
const FONT_SIZES = [
  { label: '初号', value: '42px' },
  { label: '小初', value: '36px' },
  { label: '一号', value: '26px' },
  { label: '小一', value: '24px' },
  { label: '二号', value: '22px' },
  { label: '小二', value: '18px' },
  { label: '三号', value: '16px' },
  { label: '小三', value: '15px' },
  { label: '四号', value: '14px' },
  { label: '小四', value: '12px' },
  { label: '五号', value: '10.5px' },
  { label: '小五', value: '9px' },
  { label: '六号', value: '7.5px' },
  { label: '小六', value: '6.5px' },
  { label: '默认', value: '' },
  { label: '8', value: '8px' },
  { label: '9', value: '9px' },
  { label: '10', value: '10px' },
  { label: '11', value: '11px' },
  { label: '12', value: '12px' },
  { label: '14', value: '14px' },
  { label: '16', value: '16px' },
  { label: '18', value: '18px' },
  { label: '20', value: '20px' },
  { label: '22', value: '22px' },
  { label: '24', value: '24px' },
  { label: '26', value: '26px' },
  { label: '28', value: '28px' },
  { label: '36', value: '36px' },
  { label: '48', value: '48px' },
  { label: '72', value: '72px' },
]

// 获取显示名称
const getDisplayLabel = (value: string): string => {
  if (!value) return '默认';
  
  const fontSize = FONT_SIZES.find(size => size.value === value);
  if (fontSize) return fontSize.label;
  
  // 如果是px值但未在预设列表中，则提取数字部分
  const numericSize = parseInt(value);
  return isNaN(numericSize) ? '默认' : numericSize.toString();
};

// 获取下一个更大的字体大小
const getNextLargerFontSize = (currentSize: string): string => {
  if (!currentSize) return '16px';
  
  const numericValue = parseFloat(currentSize);
  if (isNaN(numericValue)) return '16px';
  
  // 查找当前大小在预设中的位置
  const currentIndex = FONT_SIZES.findIndex(size => 
    parseFloat(size.value) === numericValue
  );
  
  if (currentIndex === -1) {
    // 如果不在预设中，找到下一个更大的预设
    const nextSize = FONT_SIZES.find(size => parseFloat(size.value) > numericValue);
    return nextSize ? nextSize.value : FONT_SIZES[0].value; // 初号是最大的
  } else if (currentIndex > 0) {
    // 返回下一个预设大小（注意中文字号是从大到小排列的）
    return FONT_SIZES[currentIndex - 1].value;
  }
  
  // 已经是最大预设，增加20%
  return `${Math.round(numericValue * 1.2)}px`;
};

// 获取下一个更小的字体大小
const getNextSmallerFontSize = (currentSize: string): string => {
  if (!currentSize) return '14px';
  
  const numericValue = parseFloat(currentSize);
  if (isNaN(numericValue)) return '14px';
  
  // 查找当前大小在预设中的位置
  const currentIndex = FONT_SIZES.findIndex(size => 
    parseFloat(size.value) === numericValue
  );
  
  if (currentIndex === -1) {
    // 如果不在预设中，找到下一个更小的预设
    const prevSize = [...FONT_SIZES].find(size => parseFloat(size.value) < numericValue);
    return prevSize ? prevSize.value : FONT_SIZES[FONT_SIZES.length - 1].value;
  } else if (currentIndex < FONT_SIZES.length - 1) {
    // 返回下一个预设大小（注意中文字号是从大到小排列的）
    return FONT_SIZES[currentIndex + 1].value;
  }
  
  // 已经是最小预设，减少20%，但不小于5px
  return `${Math.max(5, Math.round(numericValue * 0.8))}px`;
};

export type FontSizePickerProps = {
  onChange: (value: string) => void // eslint-disable-line no-unused-vars
  value: string
}

export const FontSizePicker = ({ onChange, value }: FontSizePickerProps) => {
  const currentSizeLabel = getDisplayLabel(value);

  const selectSize = useCallback((size: string) => () => onChange(size), [onChange]);
  
  const increaseFontSize = useCallback(() => {
    const nextSize = getNextLargerFontSize(value);
    onChange(nextSize);
  }, [onChange, value]);
  
  const decreaseFontSize = useCallback(() => {
    const nextSize = getNextSmallerFontSize(value);
    onChange(nextSize);
  }, [onChange, value]);

  return (
    <div className="flex items-center">
      <Dropdown.Root>
        <Dropdown.Trigger asChild>
          <Toolbar.Button active={!!value} className="min-w-[40px] px-2">
            {currentSizeLabel}
            <Icon name="ChevronDown" className="w-2 h-2 ml-1" />
          </Toolbar.Button>
        </Dropdown.Trigger>
        <Dropdown.Content
          side="bottom" 
          align="start" 
          alignOffset={0}
          sideOffset={5}
          avoidCollisions={true}
          collisionPadding={10}
          className="z-50 w-16"
        >
          <Surface className="flex flex-col px-1 py-1 max-h-[300px] overflow-y-auto">
            {FONT_SIZES.map(size => (
              <div
                key={`${size.label}_${size.value}`}
                className={`px-2 py-1 text-xs hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded cursor-pointer ${value === size.value ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : ''}`}
                onClick={() => onChange(size.value)}
              >
                {size.label}
              </div>
            ))}
          </Surface>
        </Dropdown.Content>
      </Dropdown.Root>
      
      {/* <div className="flex ml-0.5">
        <Toolbar.Button 
          tooltip="增大字号" 
          onClick={increaseFontSize}
          className="px-1 py-1"
        >
          <Icon name="ChevronUp" className="w-3 h-3" />
        </Toolbar.Button>
        <Toolbar.Button 
          tooltip="减小字号" 
          onClick={decreaseFontSize}
          className="px-1 py-1"
        >
          <Icon name="ChevronDown" className="w-3 h-3" />
        </Toolbar.Button>
      </div> */}
    </div>
  )
}
