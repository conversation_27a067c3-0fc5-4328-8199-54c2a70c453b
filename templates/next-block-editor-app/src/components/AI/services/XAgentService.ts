import { useXAgent, XStream } from '@ant-design/x';

// Define supported model providers
export type ModelProvider =
  | 'openai'
  | 'deepseek'
  | 'anthropic'
  | 'qwen'
  | 'zhipu'
  | 'grok'
  | 'google'
  | 'github'
  | 'openrouter'
  | 'mistral'
  | 'custom';

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  status?: 'loading' | 'success' | 'error';
  timestamp?: number;
}

// AI模型的配置参数
export interface AIModelConfig {
  // 模型提供商
  provider: ModelProvider;
  // 模型提供商名称（用于显示）
  providerName: string;
  // 基础URL
  baseUrl: string;
  // API密钥
  apiKey?: string;
  // 模型名称
  model: string;
  // 模型显示名称（用于显示）
  modelName: string;
  // 温度参数
  temperature?: number;
  // 最大token数
  max_tokens?: number;
  // 是否开启流式响应
  stream?: boolean;
  // 是否为默认模型
  isDefault?: boolean;
}

// 默认AI配置列表
export const DEFAULT_MODEL_CONFIGS: AIModelConfig[] = [
  {
    provider: 'deepseek',
    providerName: 'DeepSeek',
    modelName: 'DeepSeek Chat',
    baseUrl: 'https://api.deepseek.com',
    apiKey: '', 
    model: 'deepseek-chat',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: true,
  },
  {
    provider: 'openai',
    providerName: 'OpenAI',
    modelName: 'GPT-3.5 Turbo',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: '', 
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'zhipu',
    providerName: '智谱AI',
    modelName: 'GLM-4',
    baseUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions', 
    apiKey: '',
    model: 'glm-4',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'grok',
    providerName: 'Grok',
    modelName: 'Grok-1',
    baseUrl: 'https://api.x.ai/v1',
    apiKey: '',
    model: 'grok-1',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'google',
    providerName: 'Google',
    modelName: 'Gemini Pro',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta/openai/',
    apiKey: '',
    model: 'gemini-pro',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'github',
    providerName: 'GitHub (Azure)',
    modelName: 'GitHub Copilot Model',
    baseUrl: 'https://models.inference.ai.azure.com/chat/completions',
    apiKey: '',
    model: 'github-copilot',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'qwen',
    providerName: '通义千问',
    modelName: 'Qwen Turbo',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1/',
    apiKey: '',
    model: 'qwen-turbo',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'openrouter',
    providerName: 'OpenRouter',
    modelName: 'Mistral 7B Instruct',
    baseUrl: 'https://openrouter.ai/api/v1/',
    apiKey: '',
    model: 'mistralai/mistral-7b-instruct',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
  {
    provider: 'mistral',
    providerName: 'Mistral AI',
    modelName: 'Mistral Tiny',
    baseUrl: 'https://api.mistral.ai/v1/',
    apiKey: '',
    model: 'mistral-tiny',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false,
  },
];

// 默认的AI配置（第一个默认模型或第一个配置）
export const DEFAULT_MODEL_CONFIG = DEFAULT_MODEL_CONFIGS.find(config => config.isDefault) || DEFAULT_MODEL_CONFIGS[0];

// 用于保存和管理多个模型配置
export class ModelConfigManager {
  private static readonly STORAGE_KEY = 'ai-model-configs';
  
  // 检查是否在客户端环境
  static isClient(): boolean {
    return typeof window !== 'undefined';
  }
  
  // 获取所有保存的模型配置
  static getAllConfigs(): AIModelConfig[] {
    try {
      if (!this.isClient()) {
        return DEFAULT_MODEL_CONFIGS;
      }
      
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const configs = JSON.parse(stored);
        if (Array.isArray(configs) && configs.length > 0) {
          return configs;
        }
      }
    } catch (e) {
      console.error('读取模型配置失败:', e);
    }
    
    // 返回默认配置
    return DEFAULT_MODEL_CONFIGS;
  }
  
  // 保存所有模型配置
  static saveAllConfigs(configs: AIModelConfig[]): void {
    try {
      if (!this.isClient()) return;
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
    } catch (e) {
      console.error('保存模型配置失败:', e);
    }
  }
  
  // 获取当前激活的模型配置
  static getCurrentConfig(): AIModelConfig {
    try {
      if (!this.isClient()) {
        return DEFAULT_MODEL_CONFIG;
      }
      
      const activeConfigId = localStorage.getItem('ai-active-model');
      const allConfigs = this.getAllConfigs();
      
      if (activeConfigId) {
        // 通过model和provider字段组合查找匹配的配置
        const [provider, model] = activeConfigId.split('|');
        const found = allConfigs.find(c => c.provider === provider && c.model === model);
        if (found) return found;
      }
      
      // 如果没有找到活跃配置，返回默认配置
      const defaultConfig = allConfigs.find(c => c.isDefault);
      return defaultConfig || allConfigs[0];
    } catch (e) {
      console.error('获取当前模型配置失败:', e);
      return DEFAULT_MODEL_CONFIG;
    }
  }
  
  // 设置当前激活的模型配置
  static setCurrentConfig(config: AIModelConfig): void {
    try {
      if (!this.isClient()) return;
      
      const configId = `${config.provider}|${config.model}`;
      localStorage.setItem('ai-active-model', configId);
    } catch (e) {
      console.error('设置当前模型配置失败:', e);
    }
  }
  
  // 添加或更新模型配置
  static updateConfig(config: AIModelConfig): AIModelConfig[] {
    const configs = this.getAllConfigs();
    const index = configs.findIndex(c => 
      c.provider === config.provider && c.model === config.model
    );
    
    if (index >= 0) {
      configs[index] = config;
    } else {
      configs.push(config);
    }
    
    this.saveAllConfigs(configs);
    return configs;
  }
  
  // 删除模型配置
  static deleteConfig(provider: string, model: string): AIModelConfig[] {
    let configs = this.getAllConfigs();
    configs = configs.filter(c => !(c.provider === provider && c.model === model));
    
    if (configs.length === 0) {
      // 如果删除了所有配置，恢复默认配置
      configs = [...DEFAULT_MODEL_CONFIGS];
    }
    
    this.saveAllConfigs(configs);
    return configs;
  }
}

// 手动解析SSE格式数据
const parseSSEResponse = async function* (reader: ReadableStreamDefaultReader<Uint8Array>) {
  const decoder = new TextDecoder();
  let buffer = '';
  
  while (true) {
    const { value, done } = await reader.read();
    if (done) break;
    
    buffer += decoder.decode(value, { stream: true });
    
    // 按行分割
    const lines = buffer.split('\n');
    buffer = lines.pop() || ''; // 保留最后一个可能不完整的行
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue; // 跳过空行
      
      if (trimmedLine === 'data: [DONE]') {
        yield '[DONE]';
        continue;
      }
      
      // 检查是否以data:开头
      if (trimmedLine.startsWith('data: ')) {
        try {
          // 提取JSON部分
          const jsonStr = trimmedLine.slice(6);
          yield jsonStr;
        } catch (e) {
          console.error('解析SSE数据行错误:', e);
        }
      }
    }
  }
  
  // 处理可能剩余的buffer
  if (buffer.trim()) {
    if (buffer.trim() === 'data: [DONE]') {
      yield '[DONE]';
    } else if (buffer.trim().startsWith('data: ')) {
      try {
        const jsonStr = buffer.trim().slice(6);
        yield jsonStr;
      } catch (e) {
        console.error('解析剩余SSE数据错误:', e);
      }
    }
  }
};

// 不同厂商的适配器接口
interface ModelAdapter {
  prepareHeaders(config: AIModelConfig): HeadersInit;
  prepareRequestBody(messages: any[], config: AIModelConfig): any;
  parseResponse(data: any): string;
  parseStreamChunk(chunk: string): string | null;
}

// 厂商适配器工厂
class ModelAdapterFactory {
  static getAdapter(provider: ModelProvider): ModelAdapter {
    switch (provider) {
      case 'openai':
        return new OpenAIAdapter();
      case 'deepseek':
        return new DeepSeekAdapter();
      case 'anthropic':
        return new AnthropicAdapter();
      case 'qwen':
        return new OpenAIAdapter();
      case 'zhipu':
        return new OpenAIAdapter();
      case 'grok':
        return new GrokAdapter();
      case 'google':
        return new GoogleAdapter();
      case 'github':
        return new GitHubAdapter();
      case 'openrouter':
        return new OpenRouterAdapter();
      case 'mistral':
        return new MistralAdapter();
      case 'custom':
      default:
        return new CustomAdapter();
    }
  }
}

// OpenAI适配器
class OpenAIAdapter implements ModelAdapter {
  prepareHeaders(config: AIModelConfig): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey || ''}`
    };
  }
  
  prepareRequestBody(messages: any[], config: AIModelConfig): any {
    return {
      model: config.model,
      messages,
      temperature: config.temperature,
      max_tokens: config.max_tokens,
      stream: config.stream
    };
  }
  
  parseResponse(data: any): string {
    return data.choices?.[0]?.message?.content || '';
  }
  
  parseStreamChunk(chunk: string): string | null {
    try {
      const data = JSON.parse(chunk);
      return data.choices?.[0]?.delta?.content || null;
    } catch (e) {
      return null;
    }
  }
}

// DeepSeek适配器
class DeepSeekAdapter implements ModelAdapter {
  prepareHeaders(config: AIModelConfig): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey || ''}`
    };
  }
  
  prepareRequestBody(messages: any[], config: AIModelConfig): any {
    return {
      model: config.model,
      messages,
      temperature: config.temperature,
      max_tokens: config.max_tokens,
      stream: config.stream
    };
  }
  
  parseResponse(data: any): string {
    return data.choices?.[0]?.message?.content || '';
  }
  
  parseStreamChunk(chunk: string): string | null {
    try {
      const data = JSON.parse(chunk);
      return data.choices?.[0]?.delta?.content || null;
    } catch (e) {
      return null;
    }
  }
}

// Anthropic适配器
class AnthropicAdapter implements ModelAdapter {
  prepareHeaders(config: AIModelConfig): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'x-api-key': config.apiKey || '',
      'anthropic-version': '2023-06-01'
    };
  }
  
  prepareRequestBody(messages: any[], config: AIModelConfig): any {
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role !== 'system');
    
    return {
      model: config.model,
      messages: userMessages,
      system: systemMessage?.content || '',
      temperature: config.temperature,
      max_tokens: config.max_tokens,
      stream: config.stream
    };
  }
  
  parseResponse(data: any): string {
    return data.content?.[0]?.text || '';
  }
  
  parseStreamChunk(chunk: string): string | null {
    try {
      const data = JSON.parse(chunk);
      return data.delta?.text || null;
    } catch (e) {
      return null;
    }
  }
}

// Grok适配器 (extends OpenAI as structure is similar)
class GrokAdapter extends OpenAIAdapter {
  // Override to add specific logging and potentially handle Grok nuances
  parseStreamChunk(chunk: string): string | null {
    try {
      const data = JSON.parse(chunk);
      // Explicitly check for finish_reason, return null if present
      if (data.choices?.[0]?.finish_reason) {
        return null;
      }
      // Extract content like OpenAI
      return data.choices?.[0]?.delta?.content || null;
    } catch (e) {
      console.error('Error parsing Grok stream chunk:', e, 'Chunk:', chunk); // Add logging
      return null; // Return null on error
    }
  }
}

// Google 适配器 (extends OpenAI as structure might be compatible)
class GoogleAdapter extends OpenAIAdapter {}

// GitHub 适配器 (extends OpenAI as structure is compatible)
class GitHubAdapter extends OpenAIAdapter {}

// OpenRouter 适配器 (extends OpenAI, may need header overrides later)
class OpenRouterAdapter extends OpenAIAdapter {
  // Potential future overrides for headers like HTTP-Referer or X-Title
}

// Mistral 适配器 (extends OpenAI)
class MistralAdapter extends OpenAIAdapter {}

// 自定义适配器（与OpenAI兼容的API）
class CustomAdapter extends OpenAIAdapter {}

// AI通信服务类
export class XAgentService {
  private agent: any; // 使用any类型避免类型错误
  private config: AIModelConfig;
  private systemPrompt: string = '你是Ant Design X智能助手，一个基于Ant Design设计体系的AI助手，帮助用户了解和使用Ant Design X组件库。请尽可能提供准确、简洁和有帮助的回答。';
  private adapter: ModelAdapter;

  constructor(agent: any, config?: AIModelConfig) {
    this.agent = agent;
    // 安全获取配置，如果传入了配置就使用传入的，否则获取默认配置
    this.config = config || (ModelConfigManager.isClient() 
      ? ModelConfigManager.getCurrentConfig() 
      : DEFAULT_MODEL_CONFIG);
    this.adapter = ModelAdapterFactory.getAdapter(this.config.provider);
  }

  // 初始化历史消息
  async initHistory(messages: Message[]) {
    // 在实际应用中，这里可能会请求服务器获取历史记录
    return messages;
  }

  // 获取API端点
  private getApiEndpoint(): string {
    let endpoint = `${this.config.baseUrl}`;
    
    switch (this.config.provider) {
      case 'openai':
        if (!endpoint.endsWith('/v1/chat/completions')) {
          endpoint += '/v1/chat/completions';
        }
        break;
      case 'deepseek':
        if (!endpoint.endsWith('/v1/chat/completions')) {
          endpoint += '/v1/chat/completions';
        }
        break;
      case 'anthropic':
        if (!endpoint.endsWith('/v1/messages')) {
          endpoint += '/v1/messages';
        }
        break;
      case 'qwen':
        if (!endpoint.endsWith('/chat/completions')) {
          endpoint += 'chat/completions';
        }
        break;
      case 'zhipu':
        break;
      case 'grok':
        if (!endpoint.endsWith('/chat/completions')) {
          endpoint += '/chat/completions';
        }
        break;
      case 'google':
        if (!endpoint.endsWith('/chat/completions')) {
          endpoint += 'chat/completions';
        }
        break;
      case 'github':
        break;
      case 'openrouter':
        if (!endpoint.endsWith('/chat/completions')) {
          endpoint += 'chat/completions';
        }
        break;
      case 'mistral':
        if (!endpoint.endsWith('/chat/completions')) {
          endpoint += 'chat/completions';
        }
        break;
      case 'custom':
      default:
        if (!endpoint.endsWith('/v1/chat/completions')) {
          endpoint += '/v1/chat/completions';
        }
    }
    
    return endpoint;
  }

  // 发送消息并获取响应
  async sendMessage(content: string, messages: Message[] = []): Promise<{
    response: string;
    error?: string;
  }> {
    try {
      const formattedMessages = this.formatMessages(messages, content);

      // 验证API密钥
      if (!this.config.apiKey) {
        return {
          response: '',
          error: '请先配置API密钥'
        };
      }

      // 使用适配器准备请求
      const headers = this.adapter.prepareHeaders(this.config);
      const body = this.adapter.prepareRequestBody(formattedMessages, { ...this.config, stream: false });

      // 使用标准fetch API发送请求
      const response = await fetch(this.getApiEndpoint(), {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API响应错误:', response.status, errorText);
        
        if (response.status === 401) {
          return {
            response: '',
            error: 'API密钥无效或未授权'
          };
        }
        
        return {
          response: '',
          error: `API请求失败: ${response.status} ${response.statusText}`
        };
      }

      const data = await response.json();
      return {
        response: this.adapter.parseResponse(data),
      };
    } catch (error) {
      console.error('AI通信错误:', error);
      return {
        response: '',
        error: '与AI模型通信失败，请重试'
      };
    }
  }

  // 处理流式响应
  async streamResponse(
    content: string, 
    messages: Message[] = [], 
    onChunk: (chunk: string) => void, 
    onError: (error: string) => void,
    onComplete: () => void
  ): Promise<void> {
    try {
      const formattedMessages = this.formatMessages(messages, content);

      // 验证API密钥
      if (!this.config.apiKey) {
        onError('请先配置API密钥');
        return;
      }

      // 使用适配器准备请求
      const headers = this.adapter.prepareHeaders(this.config);
      const body = this.adapter.prepareRequestBody(formattedMessages, { ...this.config, stream: true });

      const response = await fetch(this.getApiEndpoint(), {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API响应错误:', response.status, errorText);
        
        if (response.status === 401) {
          onError('API密钥无效或未授权');
          return;
        }
        
        onError(`API请求失败: ${response.status} ${response.statusText}`);
        return;
      }

      if (!response.body) {
        throw new Error('流式响应体为空');
      }

      // 使用自定义SSE解析逻辑处理流式响应
      let fullText = '';
      const reader = response.body.getReader();
      
      try {
        for await (const chunk of parseSSEResponse(reader)) {
          if (chunk === '[DONE]') continue;
          
          const content = this.adapter.parseStreamChunk(chunk);
          if (content) {
            fullText += content;
            onChunk(content);
          }
        }
        
        onComplete();
      } catch (error) {
        console.error('处理流式响应数据错误:', error);
        onError('处理流式响应数据错误，请重试');
      }
    } catch (error) {
      console.error('流式响应错误:', error);
      onError('流式响应处理失败，请重试');
    }
  }

  // 使用XAgent处理流式请求
  async useXAgentStream(content: string, messages: Message[] = []): Promise<{
    response: string;
    error?: string;
  }> {
    return new Promise((resolve) => {
      let responseText = '';
      let errorMessage = '';

      if (!this.agent) {
        resolve({
          response: '',
          error: 'XAgent未初始化'
        });
        return;
      }

      const formattedMessages = this.formatMessages(messages, content);

      this.agent.request(
        { 
          messages: formattedMessages.slice(0, -1),
          message: formattedMessages[formattedMessages.length - 1].content 
        },
        {
          onSuccess: (text: any) => {
            resolve({ response: String(text) });
          },
          onUpdate: (text: any) => {
            responseText = String(text);
          },
          onError: (error: any) => {
            errorMessage = error?.message || '未知错误';
            resolve({ response: '', error: errorMessage });
          }
        }
      );
    });
  }

  // 格式化消息
  private formatMessages(messages: Message[] = [], content: string) {
    return [
      { role: 'system', content: this.systemPrompt },
      ...messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })),
      { role: 'user', content },
    ];
  }

  // 发送带附件的消息
  async sendMessageWithFiles(content: string, files: File[], messages: Message[] = []): Promise<{
    response: string;
    error?: string;
  }> {
    try {
      // 创建FormData来上传文件
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });
      
      // 添加消息内容
      formData.append('content', content);
      
      // 添加历史消息
      formData.append('messages', JSON.stringify(messages));
      formData.append('model', this.config.model);
      
      // 使用标准fetch API上传文件
      const response = await fetch('/api/chat-with-attachments', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      return {
        response: data.text || '',
      };
    } catch (error) {
      console.error('带附件的AI通信错误:', error);
      return {
        response: '',
        error: '文件上传或通信失败，请重试'
      };
    }
  }

  // 设置模型配置
  setConfig(config: AIModelConfig) {
    this.config = config;
    this.adapter = ModelAdapterFactory.getAdapter(config.provider);
    
    // 保存当前激活的模型配置
    ModelConfigManager.setCurrentConfig(config);
    
    return this;
  }

  // 获取当前配置
  getConfig(): AIModelConfig {
    return { ...this.config };
  }

  // 设置系统提示词
  setSystemPrompt(prompt: string) {
    this.systemPrompt = prompt;
    return this;
  }
  
  // 获取系统提示词
  getSystemPrompt(): string {
    return this.systemPrompt;
  }
}

// 创建XAgent服务的Hook - 集成Ant Design X的useXAgent
export const useXAgentService = () => {
  // 获取当前配置的模型
  const currentConfig = ModelConfigManager.getCurrentConfig();
  
  const [agent] = useXAgent({
    request: async (info: any, callbacks: any) => {
      const { messages, message } = info;
      const { onSuccess, onUpdate, onError } = callbacks;

      try {
        // 每次请求前重新获取当前配置
        const config = ModelConfigManager.getCurrentConfig();
        const adapter = ModelAdapterFactory.getAdapter(config.provider);
        
        // 验证API密钥
        if (!config.apiKey) {
          onError(new Error('请先配置API密钥'));
          return;
        }
        
        let content = '';

        // 使用适配器准备请求
        const headers = adapter.prepareHeaders(config);
        const messageFormatted = [
          ...(messages || []),
          { role: 'user', content: message }
        ];
        const body = adapter.prepareRequestBody(messageFormatted, config);

        // 获取API端点
        let endpoint = `${config.baseUrl}`;
        switch (config.provider) {
          case 'openai':
            if (!endpoint.endsWith('/v1/chat/completions')) {
              endpoint += '/v1/chat/completions';
            }
            break;
          case 'deepseek':
            if (!endpoint.endsWith('/v1/chat/completions')) {
              endpoint += '/v1/chat/completions';
            }
            break;
          case 'anthropic':
            if (!endpoint.endsWith('/v1/messages')) {
              endpoint += '/v1/messages';
            }
            break;
          case 'qwen':
            if (!endpoint.endsWith('/chat/completions')) {
              endpoint += 'chat/completions';
            }
            break;
          case 'zhipu':
            break;
          case 'grok':
            if (!endpoint.endsWith('/chat/completions')) {
              endpoint += '/chat/completions';
            }
            break;
          case 'google':
            if (!endpoint.endsWith('/chat/completions')) {
              endpoint += 'chat/completions';
            }
            break;
          case 'github':
            break;
          case 'openrouter':
            if (!endpoint.endsWith('/chat/completions')) {
              endpoint += 'chat/completions';
            }
            break;
          case 'mistral':
            if (!endpoint.endsWith('/chat/completions')) {
              endpoint += 'chat/completions';
            }
            break;
          default:
            if (!endpoint.endsWith('/v1/chat/completions')) {
              endpoint += '/v1/chat/completions';
            }
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          headers,
          body: JSON.stringify(body),
        });

        // 检查响应状态
        if (!response.ok) {
          const errorText = await response.text();
          console.error('API响应错误:', response.status, errorText);
          
          if (response.status === 401) {
            onError(new Error('API密钥无效或未授权'));
            return;
          }
          
          onError(new Error(`API请求失败: ${response.status} ${response.statusText}`));
          return;
        }

        if (!response.body) {
          throw new Error('流式响应体为空');
        }

        // 使用自定义SSE解析逻辑处理流式响应
        const reader = response.body.getReader();
        
        try {
          for await (const chunk of parseSSEResponse(reader)) {
            if (chunk === '[DONE]') continue;
            
            const parsedContent = adapter.parseStreamChunk(chunk);
            if (parsedContent) {
              content += parsedContent;
              onUpdate(content);
            }
          }
          
          onSuccess(content);
        } catch (error) {
          console.error('处理流式响应数据错误:', error);
          onError(new Error('处理流式响应数据错误，请重试'));
        }
      } catch (error) {
        console.error('XAgent请求错误:', error);
        onError(error instanceof Error ? error : new Error(String(error)));
      }
    },
  });

  return new XAgentService(agent, currentConfig);
}; 