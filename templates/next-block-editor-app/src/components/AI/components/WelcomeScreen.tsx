import React from 'react';
import { Welcome } from '@ant-design/x';

interface WelcomeScreenProps {
  suggestionQuestions: string[];
  handleQuestionClick: (question: string) => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  suggestionQuestions,
  handleQuestionClick
}) => {
  return (
    <div className="welcome-container">
      <Welcome
        variant="borderless"
        title="👋 你好，我是 AI 文档助手"
        description="我可以帮助您查询和了解文档内容，回答问题并提供支持。"
      />
      <div className="mt-5 text-gray-700">我可以帮您：</div>
      <div className="mt-3 space-y-2">
        {suggestionQuestions.map((question) => (
          <div 
            key={question}
            className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100"
            onClick={() => handleQuestionClick(question)}
          >
            {question}
          </div>
        ))}
      </div>
    </div>
  );
};

export default WelcomeScreen; 