import React, { useMemo } from 'react';
import { Button, Dropdown } from 'antd';
import { HistoryOutlined, PlusOutlined } from '@ant-design/icons';
import { Conversations } from '@ant-design/x';
import type { MenuProps as AntdMenuProps } from 'antd';
import { useChatStore } from '../../../store/chatStore';

interface ChatHistoryProps {
  historyDrawerVisible: boolean;
  setHistoryDrawerVisible: (visible: boolean) => void;
  handleNewConversation: () => void;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({
  historyDrawerVisible,
  setHistoryDrawerVisible,
  handleNewConversation
}) => {
  // 使用zustand store
  const { 
    conversations,
    activeConversationId,
    setActiveConversation,
    deleteConversation 
  } = useChatStore();

  // 格式化日期时间
  const formatTime = (timestamp: number, group: string) => {
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    // 对于近三天和更早的记录，显示yyyy-MM-dd HH:mm格式
    if (group === '近三天' || group === '更早') {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    
    // 今天和昨天的记录只显示时:分
    return `${hours}:${minutes}`;
  };

  // 获取日期分组
  const getDateGroup = (timestamp: number) => {
    const now = new Date();
    
    // 重置时间为当天0点
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterdayStart = todayStart - 86400000;
    const threeDaysAgoStart = todayStart - 86400000 * 3;
    
    if (timestamp >= todayStart) {
      return '今天';
    } else if (timestamp >= yesterdayStart) {
      return '昨天';
    } else if (timestamp >= threeDaysAgoStart) {
      return '近三天';
    } else {
      return '更早';
    }
  };

  // 获取历史对话列表
  const conversationItems = useMemo(() => {
    return Object.values(conversations)
      .map(chat => {
        const dateTimeStr = formatTime(chat.createdAt, getDateGroup(chat.createdAt));
        return {
          key: chat.id,
          // 将标题和时间都包含在label中，优化布局
          label: (
            <div className="flex flex-col py-1">
              <div className="text-sm font-medium truncate">{chat.title}</div>
              <div className="text-xs text-gray-400 mt-1">{dateTimeStr}</div>
            </div>
          ),
          timestamp: chat.createdAt,
          group: getDateGroup(chat.createdAt)
        };
      })
      // 按时间戳降序排序（最新的在前面）
      .sort((a, b) => b.timestamp - a.timestamp);
  }, [conversations]);

  // 处理历史对话选择
  const handleActiveChange = (key: string) => {
    setActiveConversation(key);
    setHistoryDrawerVisible(false); // 选择后关闭下拉菜单
  };

  // 处理删除对话
  const handleDeleteConversation = (id: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation(); // 阻止冒泡，避免触发选择对话
    }
    
    // 删除对话
    deleteConversation(id);
  };

  // 菜单操作
  const getConversationMenu = (conversation: any) => {
    const items: AntdMenuProps['items'] = [
      {
        key: 'delete',
        label: '删除',
        onClick: () => handleDeleteConversation(conversation.key)
      }
    ];
    
    return { items };
  };

  return (
    <Dropdown 
      trigger={['click']} 
      placement="bottomRight"
      open={historyDrawerVisible}
      onOpenChange={setHistoryDrawerVisible}
      dropdownRender={() => (
        <div className="bg-white p-3" 
          style={{ 
            width: '300px', 
            maxHeight: '500px', 
            overflow: 'auto',
            boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.15), 0 3px 6px -4px rgba(0, 0, 0, 0.2), 0 9px 28px 8px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(0, 0, 0, 0.06)'
          }}>
          <div className="flex justify-between items-center mb-3 pb-2 border-b">
            <span className="font-medium text-gray-700">对话历史</span>
          </div>
          <Conversations 
            items={conversationItems}
            activeKey={activeConversationId || undefined}
            onActiveChange={handleActiveChange}
            groupable={true}
            menu={(conversation) => getConversationMenu(conversation)}
            styles={{
              item: { 
                padding: '8px 12px',
                marginBottom: '4px',
                borderRadius: '2px'
              }
            }}
          />
        </div>
      )}
    >
      <Button 
        type="text" 
        icon={<HistoryOutlined />}
        size="middle"
      />
    </Dropdown>
  );
};

export default ChatHistory; 