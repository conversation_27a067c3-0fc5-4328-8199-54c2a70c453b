import React, { useEffect, useRef, useCallback } from 'react';
import { Bubble, Bubble as BubbleRoot } from '@ant-design/x';
import { Button, Space, theme, Typography, message } from 'antd';
import { CopyOutlined, SyncOutlined, UserOutlined, RobotOutlined, CheckOutlined } from '@ant-design/icons';
import { Message } from '../../../store/chatStore';
import markdownit from 'markdown-it';
// 移除直接导入
// import hljs from 'highlight.js';
// import 'highlight.js/styles/github.css'; // 直接引入highlight.js样式

// 客户端检测
const isClient = typeof window !== 'undefined';

// 在客户端环境下创建一个占位对象，会在useEffect中动态替换
let hljs: any = {
  getLanguage: () => false,
  highlight: () => ({ value: '' }),
  highlightElement: () => {},
  registerLanguage: () => {}
};

// 初始化markdown-it实例，但不立即配置highlight函数
const md: markdownit = markdownit({ 
  html: true, 
  breaks: true
});

// 创建markdown渲染函数
const renderMarkdown = (content: string) => (
  <Typography>
    <div
      className="markdown-content"
      dangerouslySetInnerHTML={{ __html: md.render(content) }}
    />
  </Typography>
);

interface MessageListProps {
  messages: Message[];
  handleRegenerateMessage: (messageId: string) => void;
  handleCopyMessage: (content: string) => void;
}

const MessageList: React.FC<MessageListProps> = ({ 
  messages,
  handleRegenerateMessage,
  handleCopyMessage
}) => {
  // 参考所有渲染的消息
  const messagesRef = useRef<Message[]>([]);
  
  // 使用Ant Design主题
  const { token } = theme.useToken();

  // 动态加载highlight.js库
  useEffect(() => {
    if (isClient) {
      // 动态导入highlight.js和样式
      import('highlight.js').then(hljsModule => {
        hljs = hljsModule.default;
        
        // 注册常用语言
        import('highlight.js/lib/languages/java').then(javaLang => 
          hljs.registerLanguage('java', javaLang.default));
        import('highlight.js/lib/languages/javascript').then(jsLang => 
          hljs.registerLanguage('javascript', jsLang.default));
        import('highlight.js/lib/languages/typescript').then(tsLang => 
          hljs.registerLanguage('typescript', tsLang.default));
        import('highlight.js/lib/languages/python').then(pyLang => 
          hljs.registerLanguage('python', pyLang.default));
        import('highlight.js/lib/languages/xml').then(xmlLang => 
          hljs.registerLanguage('html', xmlLang.default));
        import('highlight.js/lib/languages/css').then(cssLang => 
          hljs.registerLanguage('css', cssLang.default));
          
        // 配置markdown-it的highlight函数
        md.set({
          highlight: function (str: string, lang: string): string {
            if (lang && hljs.getLanguage(lang)) {
              try {
                return '<pre class="hljs"><code class="language-' + lang + '">' + 
                  hljs.highlight(str, { language: lang, ignoreIllegals: true }).value + 
                  '</code></pre>';
              } catch (__) {}
            }
            // 使用通用替代
            return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>';
          }
        });
        
        // 导入样式
        import('highlight.js/styles/github.css');
        
        // 应用高亮并添加复制按钮
        setTimeout(() => {
          applyHighlightAndAddButtons();
        }, 100);
      });
    }
  }, []);

  // 复制代码块内容
  const copyCodeToClipboard = useCallback((codeElement: HTMLElement) => {
    if (!isClient) return;
    
    const code = codeElement.textContent || '';
    
    navigator.clipboard.writeText(code)
      .then(() => {
        message.success('代码已复制到剪贴板');
        
        // 找到复制按钮并添加copied类
        const button = codeElement.parentElement?.querySelector('.code-copy-button');
        if (button) {
          // 临时显示成功状态
          const icon = button.querySelector('.anticon');
          const copiedIcon = document.createElement('span');
          copiedIcon.className = 'anticon';
          copiedIcon.innerHTML = '<span role="img" aria-label="check" class="anticon anticon-check"><svg viewBox="64 64 896 896" focusable="false" data-icon="check" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"></path></svg></span>';
          
          if (icon) {
            button.replaceChild(copiedIcon, icon);
            button.classList.add('copied');
            
            // 2秒后恢复原状
            setTimeout(() => {
              const originalIcon = document.createElement('span');
              originalIcon.className = 'anticon';
              originalIcon.innerHTML = '<span role="img" aria-label="copy" class="anticon anticon-copy"><svg viewBox="64 64 896 896" focusable="false" data-icon="copy" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"></path></svg></span>';
              
              const iconToReplace = button.querySelector('.anticon');
              if (iconToReplace) {
                button.replaceChild(originalIcon, iconToReplace);
              }
              button.classList.remove('copied');
            }, 2000);
          }
        }
      })
      .catch(err => {
        console.error('复制失败:', err);
        message.error('复制失败');
      });
  }, []);

  // 为代码块添加复制按钮
  const addCopyButtons = useCallback(() => {
    if (!isClient) return;
    
    document.querySelectorAll('.markdown-content pre').forEach(preBlock => {
      const codeElement = preBlock.querySelector('code');
      
      // 如果代码块不存在或已经有复制按钮，则跳过
      if (!codeElement || preBlock.querySelector('.code-copy-button')) {
        return;
      }
      
      // 创建复制按钮
      const copyButton = document.createElement('button');
      copyButton.className = 'code-copy-button';
      copyButton.innerHTML = '<span role="img" aria-label="copy" class="anticon anticon-copy"><svg viewBox="64 64 896 896" focusable="false" data-icon="copy" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"></path></svg></span>';
      copyButton.title = '复制代码';
      
      // 添加点击事件
      copyButton.addEventListener('click', () => {
        copyCodeToClipboard(codeElement as HTMLElement);
      });
      
      // 将按钮添加到代码块
      preBlock.appendChild(copyButton);
    });
  }, [copyCodeToClipboard]);

  // 应用代码高亮和添加复制按钮
  const applyHighlightAndAddButtons = useCallback(() => {
    if (!isClient) return;
    
    // 应用代码高亮
    document.querySelectorAll('pre code').forEach((block) => {
      if (hljs && block.className.indexOf('hljs') === -1) {
        hljs.highlightElement(block as HTMLElement);
      }
    });
    
    // 添加复制按钮
    addCopyButtons();
  }, [addCopyButtons]);

  // 定义气泡角色配置
  const bubbleRoles = {
    // 用户消息气泡配置
    user: {
      placement: 'end' as const,
      avatar: <UserOutlined style={{ fontSize: '20px', color: token.colorPrimary }} />,
      variant: 'outlined' as const
    },
    // AI助手消息气泡配置
    assistant: {
      placement: 'start' as const,
      avatar: <RobotOutlined style={{ fontSize: '20px', color: '#1677ff' }} />,
      variant: 'filled' as const
    }
  };

  // 渲染后对代码块应用高亮和添加复制按钮
  useEffect(() => {
    // 如果消息数组发生变化，重新应用高亮
    if (isClient && JSON.stringify(messages) !== JSON.stringify(messagesRef.current)) {
      messagesRef.current = [...messages];
      
      // 使用setTimeout确保DOM已更新
      setTimeout(() => {
        applyHighlightAndAddButtons();
      }, 10);
    }
  }, [messages, applyHighlightAndAddButtons]);

  return (
    <div className="space-y-4">
      <Bubble.List 
        items={messages.map(msg => {
          // 基本属性
          const bubbleProps = {
            content: msg.content,
            role: msg.role,
            // 将status转换为loading属性
            loading: msg.status === 'loading',
            key: msg.id,
            className: 'group', // 添加组类名用于悬停效果
            // 仅对助手消息启用markdown渲染
            messageRender: msg.role === 'assistant' ? (content: string) => renderMarkdown(content) : undefined
          };
          
          // 为AI助手消息添加底部按钮
          if (msg.role === 'assistant') {
            return {
              ...bubbleProps,
              footer: (
                <Space size={token.paddingXXS} className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<SyncOutlined />} 
                    title="重新生成" 
                    onClick={() => handleRegenerateMessage(msg.id)}
                  />
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<CopyOutlined />} 
                    title="复制"
                    onClick={() => handleCopyMessage(msg.content)}
                  />
                </Space>
              )
            };
          }
          
          return bubbleProps;
        })}
        roles={bubbleRoles}
      />
    </div>
  );
};

export default MessageList; 