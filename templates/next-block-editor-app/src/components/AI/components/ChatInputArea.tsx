import React, { useState } from 'react';
import { Sender, Suggestion, Attachments as AntAttachments, Prompts } from '@ant-design/x';
import type { PromptsProps } from '@ant-design/x';
import { Button, theme } from 'antd';
import type { GetProp } from 'antd';
import type { UploadFile, UploadProps } from 'antd';
import { 
  SendOutlined, 
  LinkOutlined, 
  CloudUploadOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  RocketOutlined
} from '@ant-design/icons';

// 定义SuggestionItems类型
type SuggestionItems = Exclude<GetProp<typeof Suggestion, 'items'>, () => void>;

interface ChatInputAreaProps {
  loading: boolean;
  inputValue: string;
  setInputValue: (value: string) => void;
  handleSendMessage: (content: string) => void;
  fileList: UploadFile[];
  setFileList: (fileList: UploadFile[]) => void;
  beforeUpload: UploadProps['beforeUpload'];
  handleFileChange: UploadProps['onChange'];
  promptItems: PromptsProps['items'];
  handlePromptClick: (info: any) => void;
}

const ChatInputArea: React.FC<ChatInputAreaProps> = ({
  loading,
  inputValue,
  setInputValue,
  handleSendMessage,
  fileList,
  setFileList,
  beforeUpload,
  handleFileChange,
  promptItems,
  handlePromptClick
}) => {
  // 使用Ant Design主题
  const { token } = theme.useToken();

  // 状态管理
  const [suggestionOpen, setSuggestionOpen] = useState(false);
  const [attachmentsOpen, setAttachmentsOpen] = useState(false);

  // Suggestion快捷指令数据
  const suggestionItems: SuggestionItems = [
    { label: '法律分析', value: '法律分析', icon: <InfoCircleOutlined /> },
    { label: '风险评估', value: '风险评估', icon: <WarningOutlined /> },
    { 
      label: '文档操作',
      value: 'doc',
      icon: <RocketOutlined />,
      children: [
        {
          label: '文档翻译',
          value: '将文档翻译成中文',
        },
        {
          label: '内容提炼',
          value: '提炼文档主要内容',
        },
        {
          label: '格式优化',
          value: '优化文档格式',
        },
      ],
    },
  ];

  // 处理Suggestion选择
  const handleSuggestionSelect = (value: string) => {
    setInputValue(`${value}: `);
  };

  return (
    <div className="input-area p-4">
      {/* 添加Prompts提示集 */}
      <div className="mb-4">
        <Prompts
          title="✨"
          items={promptItems}
          onItemClick={handlePromptClick}
        />
      </div>
      
      <Suggestion
        items={suggestionItems}
        open={suggestionOpen}
        onOpenChange={setSuggestionOpen}
        onSelect={handleSuggestionSelect}
      >
        {({ onTrigger, onKeyDown }) => (
          <Sender
            loading={loading}
            submitType="enter"
            value={inputValue}
            onChange={(nextVal) => {
              if (nextVal === '/') {
                onTrigger();
              } else if (nextVal && suggestionOpen && !nextVal.startsWith('/')) {
                onTrigger(false);
              }
              setInputValue(nextVal);
            }}
            onKeyDown={onKeyDown}
            onSubmit={(message) => {
              if (message.trim()) {
                handleSendMessage(message);
                setInputValue('');
              }
            }}
            allowSpeech
            placeholder="问我问题或输入 / 使用技能"
            actions={(originalActions, { components }) => (
              <Button 
                type="text" 
                icon={<SendOutlined />} 
                style={{ color: token.colorPrimary }}
                loading={loading}
                onClick={() => {
                  if (inputValue.trim()) {
                    handleSendMessage(inputValue);
                    setInputValue('');
                  }
                }}
              />
            )}
            header={
              <Sender.Header
                title="上传文件"
                styles={{
                  content: {
                    padding: 0,
                  },
                }}
                open={attachmentsOpen}
                onOpenChange={setAttachmentsOpen}
                forceRender
              >
                <AntAttachments
                  beforeUpload={beforeUpload}
                  onChange={handleFileChange}
                  items={fileList}
                  placeholder={{
                    icon: <CloudUploadOutlined />,
                    title: '上传文件',
                    description: '点击或将文件拖拽到此区域上传',
                  }}
                />
              </Sender.Header>
            }
            prefix={
              <button
                type="button"
                className="p-2 text-gray-500 hover:text-gray-700"
                onClick={() => setAttachmentsOpen(!attachmentsOpen)}
              >
                <LinkOutlined />
              </button>
            }
          />
        )}
      </Suggestion>
    </div>
  );
};

export default ChatInputArea; 