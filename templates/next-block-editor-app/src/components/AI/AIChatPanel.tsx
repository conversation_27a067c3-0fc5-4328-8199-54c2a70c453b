import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Tooltip, theme, message, Modal, Tabs, Select, Space } from 'antd';
import { PlusOutlined, CloseOutlined, SettingOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import type { PromptsProps } from '@ant-design/x';
import { useChatStore } from '../../store/chatStore';
import { 
  AIModelConfig, 
  ModelConfigManager, 
  ModelProvider 
} from './services/XAgentService';

// 导入模型配置相关组件和工具函数
import {
  ModelManagementPanel,
  showAddModelConfigModal,
  showEditModelConfigModal,
  handleDeleteModelConfig,
  handleChangeCurrentModel,
  handleSaveModelConfig
} from './utils/ModelConfigManager';

// 导入拆分的组件
import ChatInputArea from './components/ChatInputArea';
import ChatHistory from './components/ChatHistory';
import MessageList from './components/MessageList';
import WelcomeScreen from './components/WelcomeScreen';

// 组件接口
interface AIChatPanelProps {
  onClose?: () => void;
}

// ========== Prompt 管理模块 ========== 
type PromptItem = {
  id: string;
  title: string;
  content: string;
  purpose: string;
  models: string[];
  source: string;
  createdAt: string;
};
const PROMPT_STORAGE_KEY = 'global_prompts';
const PromptManager = {
  getAllPrompts(): PromptItem[] {
    try {
      const data = localStorage.getItem(PROMPT_STORAGE_KEY)
      return data ? JSON.parse(data) : []
    } catch {
      return []
    }
  },
  addPrompt(prompt: PromptItem) {
    const prompts = this.getAllPrompts()
    prompts.push(prompt)
    localStorage.setItem(PROMPT_STORAGE_KEY, JSON.stringify(prompts))
  },
  updatePrompt(id: string, update: Partial<PromptItem>) {
    const prompts = this.getAllPrompts().map(p =>
      p.id === id ? { ...p, ...update } : p
    )
    localStorage.setItem(PROMPT_STORAGE_KEY, JSON.stringify(prompts))
  },
  deletePrompt(id: string) {
    const prompts = this.getAllPrompts().filter(p => p.id !== id)
    localStorage.setItem(PROMPT_STORAGE_KEY, JSON.stringify(prompts))
  },
  getPromptById(id: string): PromptItem | undefined {
    return this.getAllPrompts().find(p => p.id === id)
  },
}

const AIChatPanel: React.FC<AIChatPanelProps> = ({ onClose }) => {
  // 使用zustand store
  const { 
    conversations,
    activeConversationId,
    setActiveConversation,
    addConversation,
    deleteConversation,
    sendMessage,
    regenerateMessage,
    updateApiConfig
  } = useChatStore();
  
  // 状态管理
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [historyDrawerVisible, setHistoryDrawerVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [currentSettingsTab, setCurrentSettingsTab] = useState<string>('models');
  const [modelConfigs, setModelConfigs] = useState<AIModelConfig[]>(ModelConfigManager.getAllConfigs());
  const [currentModelId, setCurrentModelId] = useState<string>(
    `${ModelConfigManager.getCurrentConfig().provider}|${ModelConfigManager.getCurrentConfig().model}`
  );
  
  // 获取当前会话的消息
  const messages = activeConversationId ? conversations[activeConversationId]?.messages || [] : [];
  
  // 使用Ant Design主题
  const { token } = theme.useToken();
  
  // 引用
  const conversationsRef = useRef<HTMLDivElement>(null);
  
  // 监听活跃会话变化
  useEffect(() => {
    if (activeConversationId) {
      // 只有当会话有消息时才隐藏欢迎页面
      const hasMessages = conversations[activeConversationId]?.messages.length > 0;
      setShowWelcome(!hasMessages);
    } else {
      setShowWelcome(true);
    }
  }, [activeConversationId, conversations]);

  // 刷新模型配置列表
  const refreshModelConfigs = () => {
    const configs = ModelConfigManager.getAllConfigs();
    setModelConfigs(configs);
    
    // 刷新当前活跃模型ID
    const currentConfig = ModelConfigManager.getCurrentConfig();
    setCurrentModelId(`${currentConfig.provider}|${currentConfig.model}`);
  };
  
  // 在组件挂载时加载模型配置
  useEffect(() => {
    refreshModelConfigs();
  }, []);
  
  // 首次加载时插入演示Prompt
  useEffect(() => {
    if (PromptManager.getAllPrompts().length === 0) {
      const demoPrompts: PromptItem[] = [
        {
          id: 'demo1',
          title: '法律观点提炼',
          content: '请帮我提炼文档中的法律观点。',
          purpose: '法律分析',
          models: [],
          source: '演示',
          createdAt: new Date().toISOString(),
        },
        {
          id: 'demo2',
          title: '风险审查',
          content: '请帮我审查文档中的法律风险。',
          purpose: '风险评估',
          models: [],
          source: '演示',
          createdAt: new Date().toISOString(),
        },
        {
          id: 'demo3',
          title: '翻译成中文',
          content: '请将下列内容翻译成中文。',
          purpose: '翻译',
          models: [],
          source: '演示',
          createdAt: new Date().toISOString(),
        },
        {
          id: 'demo4',
          title: '全文总结',
          content: '请帮我总结全文。',
          purpose: '总结',
          models: [],
          source: '演示',
          createdAt: new Date().toISOString(),
        },
      ];
      demoPrompts.forEach(p => PromptManager.addPrompt(p));
      setPromptList(PromptManager.getAllPrompts());
    }
  }, []);
  
  // 快捷提问选项
  const suggestionQuestions = [
    '如何使用文档中的代码示例?', 
    '文档有哪些主要的章节?', 
    '如何快速查找我需要的内容?'
  ];
  
  // 复制消息内容
  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        message.success('文本已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
        message.error('复制失败');
      });
  };
  
  // 处理消息发送
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;
    
    // 首次发送消息时隐藏欢迎组件
    if (showWelcome) {
      setShowWelcome(false);
    }
    
    setInputValue('');
    setLoading(true);
    setError(null);
    
    try {
      // 等待sendMessage完成（包括流式响应完成）
      await sendMessage(content);
    } catch (err) {
      setError('消息发送失败，请检查API配置');
      console.error('发送消息错误:', err);
    } finally {
      // 只有当sendMessage完全结束时才更新loading状态
      setLoading(false);
    }
  };
  
  // 处理问题点击
  const handleQuestionClick = (question: string) => {
    handleSendMessage(question);
  };
  
  // 上传文件前验证
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 模拟上传
    return false;
  };
  
  // 文件变更处理
  const handleFileChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };
  
  // 滚动到最新消息
  useEffect(() => {
    if (conversationsRef.current) {
      conversationsRef.current.scrollTop = conversationsRef.current.scrollHeight;
    }
  }, [messages]);
  
  // 创建新对话
  const handleNewConversation = () => {
    // 创建新会话
    addConversation();
  };

  // 当前对话标题
  const currentTitle = activeConversationId && conversations[activeConversationId] 
    ? conversations[activeConversationId].title 
    : '新对话';

  // 处理关闭面板
  const handleClose = () => {
    // 如果有提供关闭回调，则调用
    if (onClose) {
      onClose();
    }
  };

  // 打开设置弹窗
  const showSettings = () => {
    setSettingsVisible(true);
  };

  // 模型配置操作包装函数
  const handleAddNewModel = (provider: ModelProvider) => {
    showAddModelConfigModal(provider, (values) => 
      handleSaveModelConfig(values, refreshModelConfigs, updateApiConfig)
    );
  };

  const handleEditConfig = (config: AIModelConfig) => {
    showEditModelConfigModal(config, (values) => 
      handleSaveModelConfig(values, refreshModelConfigs, updateApiConfig)
    );
  };

  const handleDeleteConfig = (provider: string, model: string) => {
    handleDeleteModelConfig(provider, model, refreshModelConfigs, updateApiConfig);
  };

  const handleUseConfig = (config: AIModelConfig) => {
    ModelConfigManager.setCurrentConfig(config);
    updateApiConfig();
    refreshModelConfigs();
  };

  const handleSelectModel = (modelId: string) => {
    handleChangeCurrentModel(modelId, setCurrentModelId, updateApiConfig);
  };

  // Prompts提示集数据
  const promptItems: PromptsProps['items'] = [
    {
      key: '1',
      label: '提炼',
      description: '帮我提炼法律观点？',
    },
    {
      key: '2',
      label: '审查',
      description: '审查存在的法律风险？',
    },
    {
      key: '3',
      label: '翻译',
      description: '帮我翻译成中文？',
    },
    {
      key: '4',
      label: '更多',
      description: '帮我总结全文？',
    },
  ];

  // 处理提示点击
  const handlePromptClick = (info: any) => {
    handleSendMessage(info.data.description);
  };

  // Prompt管理UI状态
  const [promptList, setPromptList] = useState<PromptItem[]>(PromptManager.getAllPrompts())
  const [promptForm, setPromptForm] = useState<Partial<PromptItem>>({})
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null)
  const [promptQuery, setPromptQuery] = useState('')
  const [promptModalVisible, setPromptModalVisible] = useState(false)

  const filteredPrompts = promptList.filter(item => {
    const q = promptQuery.trim().toLowerCase()
    if (!q) return true
    return (
      item.title.toLowerCase().includes(q) ||
      item.content.toLowerCase().includes(q) ||
      (item.purpose || '').toLowerCase().includes(q) ||
      (item.source || '').toLowerCase().includes(q)
    )
  })

  const refreshPrompts = () => setPromptList(PromptManager.getAllPrompts())

  const handlePromptNew = () => {
    setEditingPromptId(null)
    setPromptForm({})
    setPromptModalVisible(true)
  }
  const handlePromptEdit = (item: PromptItem) => {
    setEditingPromptId(item.id)
    setPromptForm({ ...item })
    setPromptModalVisible(true)
  }
  const handlePromptDelete = (id: string) => {
    PromptManager.deletePrompt(id)
    refreshPrompts()
    message.success('已删除')
  }
  const handlePromptFormSave = () => {
    if (!promptForm.title || !promptForm.content) return message.error('标题和内容必填')
    if (editingPromptId) {
      PromptManager.updatePrompt(editingPromptId, { ...promptForm })
      setEditingPromptId(null)
    } else {
      PromptManager.addPrompt({
        id: Date.now().toString(),
        title: promptForm.title!,
        content: promptForm.content!,
        purpose: promptForm.purpose || '',
        models: promptForm.models || [],
        source: promptForm.source || '',
        createdAt: new Date().toISOString(),
      })
    }
    setPromptForm({})
    setPromptModalVisible(false)
    refreshPrompts()
    message.success('已保存')
  }
  const handlePromptFormCancel = () => {
    setEditingPromptId(null)
    setPromptForm({})
    setPromptModalVisible(false)
  }

  return (
    <div className="ai-chat-panel h-full flex flex-col">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center">
          <span className="text-lg font-medium">AI 文档助手</span>
        </div>
        <div className="flex items-center">
          {/* 模型选择器移到右侧按钮组前面 */}
          <div className="flex items-center">
            {/* <span className="text-xs text-gray-500 mr-1">模型:</span> */}
            <Select
              value={currentModelId}
              onChange={handleSelectModel}
              style={{ width: 120 }}
              dropdownMatchSelectWidth={false}
              placeholder="选择模型"
              optionLabelProp="label"
              popupClassName="model-select-dropdown"
              options={modelConfigs.map(config => ({
                // label: `${config.providerName} - ${config.modelName}`,
                label: `${config.modelName}`,
                value: `${config.provider}|${config.model}`,
              }))}
              optionRender={(option) => (
                <Space>
                  {/* <span role="img" aria-label={option.data.label.split(' - ')[0]}>
                    {option.data.label.split(' - ')[0] === 'OpenAI' ? '🟢' : 
                     option.data.label.split(' - ')[0] === 'DeepSeek' ? '🔵' :
                     option.data.label.split(' - ')[0] === 'Anthropic' ? '🟣' :
                     option.data.label.split(' - ')[0] === '通义千问' ? '🟠' : '⚪'}
                  </span> */}
                  {option.data.label}
                </Space>
              )}
              // suffixIcon={<div className="text-xs text-blue-500 opacity-80">切换</div>}
            />
          </div>
          
          <Tooltip title="模型设置">
            <Button 
              type="text"
              icon={<SettingOutlined />}
              size="middle"
              // className="mr-1"
              onClick={showSettings}
            />
          </Tooltip>
          
          <Tooltip title="新建会话">
            <Button 
              type="text"
              icon={<PlusOutlined />}
              size="middle"
              // className="mr-1"
              onClick={handleNewConversation}
            />
          </Tooltip>
          
          {/* 历史对话组件 */}
          <ChatHistory 
            historyDrawerVisible={historyDrawerVisible}
            setHistoryDrawerVisible={setHistoryDrawerVisible}
            handleNewConversation={handleNewConversation}
          />
          
          <Button 
            type="text" 
            icon={<CloseOutlined />} 
            size="middle" 
            // className="ml-1" 
            title="关闭" 
            onClick={handleClose}
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4" ref={conversationsRef}>
        {/* 当前会话标题或新对话标题 */}
        {!showWelcome && (
          <div className="mb-4 text-center">
            <h3 className="text-base font-medium">{currentTitle}</h3>
          </div>
        )}
        
        {/* 欢迎页 */}
        {showWelcome && (
          <WelcomeScreen 
            suggestionQuestions={suggestionQuestions}
            handleQuestionClick={handleQuestionClick}
          />
        )}
        
        {/* 对话消息列表 */}
        {!showWelcome && messages.length > 0 && (
          <MessageList 
            messages={messages}
            handleRegenerateMessage={regenerateMessage}
            handleCopyMessage={handleCopyMessage}
          />
        )}
        
        {/* 错误提示 */}
        {error && (
          <div className="my-3">
            <Alert 
              message={error} 
              type="error" 
              showIcon 
              closable 
              onClose={() => setError(null)} 
            />
          </div>
        )}
      </div>
      
      {/* 底部输入区域 */}
      <ChatInputArea 
        loading={loading}
        inputValue={inputValue}
        setInputValue={setInputValue}
        handleSendMessage={handleSendMessage}
        fileList={fileList}
        setFileList={setFileList}
        beforeUpload={beforeUpload}
        handleFileChange={handleFileChange}
        promptItems={promptItems}
        handlePromptClick={handlePromptClick}
      />

      {/* API配置弹窗 */}
      <Modal
        title="模型设置"
        open={settingsVisible}
        footer={null}
        onCancel={() => setSettingsVisible(false)}
        width={800}
        className="model-settings-modal"
      >
        <Tabs 
          activeKey={currentSettingsTab}
          onChange={setCurrentSettingsTab}
          items={[
            {
              key: 'models',
              label: '模型管理',
              children: (
                <div className="max-h-[calc(60vh-40px)] overflow-y-auto pr-4"> 
                  <ModelManagementPanel
                    modelConfigs={modelConfigs}
                    currentModelId={currentModelId}
                    handleAddNewModel={handleAddNewModel}
                    handleEditModelConfig={handleEditConfig}
                    handleUseModelConfig={handleUseConfig}
                    refreshModelConfigs={refreshModelConfigs}
                    updateApiConfig={updateApiConfig}
                  />
                </div>
              )
            },
            {
              key: 'prompts',
              label: 'Prompt管理',
              children: (
                <div className="max-h-[calc(60vh-40px)] overflow-y-auto pr-4">
                  <div className="flex items-center mb-3 gap-2">
                    <input
                      className="flex-1 px-2 py-1 border rounded text-sm"
                      placeholder="搜索Prompt（标题/内容/用途/来源）"
                      value={promptQuery}
                      onChange={e => setPromptQuery(e.target.value)}
                    />
                    <Button type="primary" onClick={handlePromptNew}>新增Prompt</Button>
                  </div>
                  <div className="space-y-2 mb-4">
                    {filteredPrompts.length === 0 && <div className="text-gray-400 text-sm">暂无匹配Prompt</div>}
                    {filteredPrompts.map(promptItem => (
                      <div key={promptItem.id} className="border rounded p-2 flex flex-col gap-1 bg-gray-50 hover:bg-blue-50 transition cursor-pointer">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-blue-700" onClick={() => handlePromptEdit(promptItem)}>{promptItem.title}</span>
                          <div className="flex gap-2">
                            <Button size="small" type="link" onClick={() => handlePromptEdit(promptItem)}>编辑</Button>
                            <Button size="small" type="link" danger onClick={() => handlePromptDelete(promptItem.id)}>删除</Button>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 break-all line-clamp-2">{promptItem.content}</div>
                        <div className="text-xs text-gray-400">用途: {promptItem.purpose || '-'}</div>
                        <div className="text-xs text-gray-400">适用模型: {promptItem.models?.join(',') || '-'}</div>
                        <div className="text-xs text-gray-400">来源: {promptItem.source || '-'}</div>
                        <div className="text-xs text-gray-400">创建时间: {promptItem.createdAt ? new Date(promptItem.createdAt).toLocaleString() : '-'}</div>
                      </div>
                    ))}
                  </div>
                  <Modal
                    title={editingPromptId ? '编辑Prompt' : '新建Prompt'}
                    open={promptModalVisible}
                    onCancel={handlePromptFormCancel}
                    onOk={handlePromptFormSave}
                    okText={editingPromptId ? '保存' : '添加'}
                    cancelText="取消"
                  >
                    <>
                      <input
                        className="w-full mb-2 px-2 py-1 border rounded text-sm"
                        placeholder="标题"
                        value={promptForm.title || ''}
                        onChange={e => setPromptForm(f => ({ ...f, title: e.target.value }))}
                      />
                      <textarea
                        className="w-full mb-2 px-2 py-1 border rounded text-sm"
                        placeholder="内容"
                        value={promptForm.content || ''}
                        onChange={e => setPromptForm(f => ({ ...f, content: e.target.value }))}
                      />
                      <input
                        className="w-full mb-2 px-2 py-1 border rounded text-sm"
                        placeholder="用途"
                        value={promptForm.purpose || ''}
                        onChange={e => setPromptForm(f => ({ ...f, purpose: e.target.value }))}
                      />
                      <input
                        className="w-full mb-2 px-2 py-1 border rounded text-sm"
                        placeholder="适用模型（逗号分隔）"
                        value={promptForm.models ? promptForm.models.join(',') : ''}
                        onChange={e => setPromptForm(f => ({ ...f, models: e.target.value.split(',').map(s => s.trim()).filter(Boolean) }))}
                      />
                      <input
                        className="w-full mb-2 px-2 py-1 border rounded text-sm"
                        placeholder="来源"
                        value={promptForm.source || ''}
                        onChange={e => setPromptForm(f => ({ ...f, source: e.target.value }))}
                      />
                    </>
                  </Modal>
                </div>
              )
            },
            {
              key: 'system',
              label: '系统设置',
              children: (
                <div className="max-h-[calc(60vh-40px)] overflow-y-auto pr-4">
                  <div>
                    <p>暂无系统设置选项</p>
                  </div>
                </div>
              )
            }
          ]}
        />
      </Modal>
    </div>
  );
};

export default AIChatPanel; 