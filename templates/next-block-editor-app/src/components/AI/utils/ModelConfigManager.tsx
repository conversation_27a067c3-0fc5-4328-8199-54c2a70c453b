import React, { useState } from 'react';
import { Button, Form, Input, Modal, Space, message, Dropdown, Menu } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { 
  AIModelConfig, 
  ModelConfigManager, 
  DEFAULT_MODEL_CONFIGS, 
  ModelProvider 
} from '../services/XAgentService';

// 从本地存储获取API配置
export const getStoredAPIConfig = (): AIModelConfig => {
  try {
    // 使用ModelConfigManager获取当前模型配置
    return ModelConfigManager.getCurrentConfig();
  } catch (e) {
    console.error('读取API配置失败:', e);
  }
  return DEFAULT_MODEL_CONFIGS[0];
};

// 保存API配置到本地存储
export const saveAPIConfig = (config: AIModelConfig) => {
  try {
    // 使用ModelConfigManager保存模型配置
    ModelConfigManager.updateConfig(config);
  } catch (e) {
    console.error('保存API配置失败:', e);
  }
};

// 全局XAgentService更新配置方法
export const updateGlobalXAgentConfig = (config: AIModelConfig) => {
  try {
    // 将配置保存到ModelConfigManager中
    ModelConfigManager.updateConfig(config);
    
    // 设置为当前活跃模型
    ModelConfigManager.setCurrentConfig(config);
    
    return true;
  } catch (e) {
    console.error('更新全局XAgent配置失败:', e);
    return false;
  }
};

// 模型设置表单组件
export const ModelSettingForm: React.FC<{
  initialValues: AIModelConfig;
  onFinish: (values: AIModelConfig) => void;
  provider: ModelProvider;
}> = ({ initialValues, onFinish, provider }) => {
  return (
    <Form
      initialValues={initialValues}
      onFinish={onFinish}
      layout="vertical"
    >
      <Form.Item
        label="模型名称"
        name="modelName"
        rules={[{ required: true, message: '请输入模型显示名称' }]}
      >
        <Input placeholder="例如: DeepSeek Chat" />
      </Form.Item>

      <Form.Item
        label="API接口地址"
        name="baseUrl"
        rules={[{ required: true, message: '请输入API接口地址' }]}
      >
        <Input placeholder="例如: https://api.deepseek.com" />
      </Form.Item>

      <Form.Item
        label="API密钥"
        name="apiKey"
        rules={[{ required: true, message: '请输入API密钥' }]}
      >
        <Input.Password placeholder="例如: sk-xxxxxxxx" />
      </Form.Item>

      <Form.Item
        label="模型ID"
        name="model"
        rules={[{ required: true, message: '请输入模型ID' }]}
      >
        <Input placeholder="例如: deepseek-chat" />
      </Form.Item>

      <Form.Item
        label="温度参数"
        name="temperature"
        rules={[{ 
          type: 'number', 
          min: 0, 
          max: 1, 
          message: '温度应在0-1之间' 
        }]}
      >
        <Input type="number" step={0.1} placeholder="例如: 0.7" />
      </Form.Item>

      <Form.Item
        label="最大Token数"
        name="max_tokens"
        rules={[{ type: 'number', min: 1, message: '请输入有效的Token数' }]}
      >
        <Input type="number" placeholder="例如: 1000" />
      </Form.Item>

      <Form.Item
        label="设为默认模型"
        name="isDefault"
        valuePropName="checked"
      >
        <input type="checkbox" />
      </Form.Item>

      {/* 这些隐藏字段用于保存不在表单中显示的值 */}
      <Form.Item name="provider" hidden={true}>
        <Input />
      </Form.Item>
      
      <Form.Item name="providerName" hidden={true}>
        <Input />
      </Form.Item>
      
      <Form.Item name="stream" hidden={true}>
        <Input />
      </Form.Item>

      <Form.Item>
        <Button htmlType="submit" block>
          保存设置
        </Button>
      </Form.Item>
    </Form>
  );
};

// 处理模型配置保存
export const handleSaveModelConfig = (
  values: AIModelConfig, 
  refreshModelConfigs: () => void, 
  updateApiConfig: () => void
) => {
  // 保存或更新模型配置
  ModelConfigManager.updateConfig(values);
  
  // 如果是默认模型，需要更新其他模型的默认状态
  if (values.isDefault) {
    const configs = ModelConfigManager.getAllConfigs();
    configs.forEach(config => {
      if (config.provider !== values.provider || config.model !== values.model) {
        config.isDefault = false;
        ModelConfigManager.updateConfig(config);
      }
    });
  }
  
  // 设置为当前活跃模型
  ModelConfigManager.setCurrentConfig(values);
  
  // 通知chatStore更新配置
  updateApiConfig();
  
  // 刷新模型配置列表
  refreshModelConfigs();
  
  // 通知用户配置已保存
  message.success('模型配置已保存');
};

// 删除模型配置
export const handleDeleteModelConfig = (
  provider: string, 
  model: string, 
  refreshModelConfigs: () => void, 
  updateApiConfig: () => void
) => {
  Modal.confirm({
    title: '删除模型配置',
    content: '确定要删除这个模型配置吗？此操作不可恢复。',
    onOk: () => {
      // 删除模型配置
      ModelConfigManager.deleteConfig(provider, model);
      
      // 刷新模型配置列表
      refreshModelConfigs();
      
      // 确保chatStore使用的是有效的配置
      updateApiConfig();
      
      message.success('模型配置已删除');
    }
  });
};

// 切换当前使用的模型
export const handleChangeCurrentModel = (
  modelId: string, 
  setCurrentModelId: (id: string) => void, 
  updateApiConfig: () => void
) => {
  try {
    const [provider, model] = modelId.split('|');
    const configs = ModelConfigManager.getAllConfigs();
    const targetConfig = configs.find(c => c.provider === provider && c.model === model);
    
    if (targetConfig) {
      // 设置为当前活跃模型
      ModelConfigManager.setCurrentConfig(targetConfig);
      
      // 更新当前模型ID
      setCurrentModelId(modelId);
      
      // 通知chatStore更新配置
      updateApiConfig();
      
      message.success(`已切换到 ${targetConfig.providerName} - ${targetConfig.modelName}`);
    }
  } catch (e) {
    console.error('切换模型失败:', e);
    message.error('切换模型失败');
  }
};

// 创建新的模型配置对象
export const createModelConfig = (provider: ModelProvider): AIModelConfig => {
  let providerName = '';
  switch (provider) {
    case 'openai': providerName = 'OpenAI'; break;
    case 'deepseek': providerName = 'DeepSeek'; break;
    case 'anthropic': providerName = 'Anthropic'; break;
    case 'qwen': providerName = '通义千问'; break;
    case 'zhipu': providerName = '智谱AI'; break;
    case 'grok': providerName = 'Grok'; break;
    case 'google': providerName = 'Google'; break;
    case 'github': providerName = 'GitHub (Azure)'; break;
    case 'openrouter': providerName = 'OpenRouter'; break;
    case 'mistral': providerName = 'Mistral AI'; break;
    case 'custom': providerName = '自定义'; break;
  }
  
  const newConfig: AIModelConfig = {
    provider,
    providerName,
    modelName: '',
    baseUrl: '',
    apiKey: '',
    model: '',
    temperature: 0.7,
    max_tokens: 1000,
    stream: true,
    isDefault: false
  };
  
  switch (provider) {
    case 'openai':
      newConfig.baseUrl = 'https://api.openai.com/v1/';
      newConfig.model = 'gpt-3.5-turbo';
      break;
    case 'deepseek':
      newConfig.baseUrl = 'https://api.deepseek.com';
      newConfig.model = 'deepseek-chat';
      break;
    case 'anthropic':
      newConfig.baseUrl = 'https://api.anthropic.com';
      newConfig.model = 'claude-3-haiku-20240307';
      break;
    case 'qwen':
      newConfig.baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1/';
      newConfig.model = 'qwen-turbo';
      break;
    case 'zhipu':
      newConfig.baseUrl = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
      newConfig.model = 'glm-4';
      break;
    case 'grok':
      newConfig.baseUrl = 'https://api.x.ai/v1';
      newConfig.model = 'grok-3-beta';
      break;
    case 'google':
      newConfig.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/';
      newConfig.model = 'gemini-pro';
      break;
    case 'github':
      newConfig.baseUrl = 'https://models.inference.ai.azure.com/chat/completions';
      newConfig.model = 'github-copilot';
      break;
    case 'openrouter':
      newConfig.baseUrl = 'https://openrouter.ai/api/v1/';
      newConfig.model = 'mistralai/mistral-7b-instruct';
      newConfig.modelName = 'Mistral 7B Instruct';
      break;
    case 'mistral':
      newConfig.baseUrl = 'https://api.mistral.ai/v1/';
      newConfig.model = 'mistral-tiny';
      newConfig.modelName = 'Mistral Tiny';
      break;
  }
  
  return newConfig;
};

// 新建模型配置弹窗
export const showAddModelConfigModal = (
  provider: ModelProvider, 
  handleSaveCallback: (values: AIModelConfig) => void
) => {
  const newConfig = createModelConfig(provider);
  const providerName = newConfig.providerName;
  
  // 打开表单对话框
  Modal.confirm({
    title: `添加${providerName}模型配置`,
    width: 600,
    content: (
      <ModelSettingForm 
        initialValues={newConfig} 
        onFinish={handleSaveCallback}
        provider={provider}
      />
    ),
    footer: null
  });
};

// 编辑模型配置弹窗
export const showEditModelConfigModal = (
  config: AIModelConfig,
  handleSaveCallback: (values: AIModelConfig) => void
) => {
  // 打开表单对话框
  Modal.confirm({
    title: `编辑${config.providerName}模型配置`,
    width: 600,
    content: (
      <ModelSettingForm 
        initialValues={config} 
        onFinish={handleSaveCallback}
        provider={config.provider}
      />
    ),
    footer: null
  });
};

// 模型管理面板组件
export const ModelManagementPanel: React.FC<{
  modelConfigs: AIModelConfig[];
  currentModelId: string;
  handleAddNewModel: (provider: ModelProvider) => void;
  handleEditModelConfig: (config: AIModelConfig) => void;
  handleUseModelConfig: (config: AIModelConfig) => void;
  refreshModelConfigs: () => void;
  updateApiConfig: () => void;
}> = ({
  modelConfigs,
  currentModelId,
  handleAddNewModel,
  handleEditModelConfig,
  handleUseModelConfig,
  refreshModelConfigs,
  updateApiConfig
}) => {
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [configToDelete, setConfigToDelete] = useState<{ provider: string; model: string } | null>(null);

  const showDeleteConfirmModal = (provider: string, model: string) => {
    setConfigToDelete({ provider, model });
    setIsConfirmModalVisible(true);
  };

  const handleConfirmDelete = () => {
    if (configToDelete) {
      try {
        ModelConfigManager.deleteConfig(configToDelete.provider, configToDelete.model);
        
        refreshModelConfigs();
        
        updateApiConfig();
        
        message.success('模型配置已删除');
      } catch (error) {
        console.error("Error deleting config:", error);
        message.error('删除失败');
      } finally {
        setIsConfirmModalVisible(false);
        setConfigToDelete(null);
      }
    }
  };

  const handleCancelDelete = () => {
    setIsConfirmModalVisible(false);
    setConfigToDelete(null);
  };

  const addModelMenu = (
    <Menu onClick={({ key }) => handleAddNewModel(key as ModelProvider)}>
      <Menu.Item key="deepseek">添加DeepSeek模型</Menu.Item>
      <Menu.Item key="openai">添加OpenAI模型</Menu.Item>
      <Menu.Item key="anthropic">添加Anthropic模型</Menu.Item>
      <Menu.Item key="qwen">添加通义千问模型</Menu.Item>
      <Menu.Item key="zhipu">添加智谱AI模型</Menu.Item>
      <Menu.Item key="grok">添加Grok模型</Menu.Item>
      <Menu.Item key="google">添加Google模型</Menu.Item>
      <Menu.Item key="github">添加GitHub模型</Menu.Item>
      <Menu.Item key="openrouter">添加OpenRouter模型</Menu.Item>
      <Menu.Item key="mistral">添加Mistral模型</Menu.Item>
      <Menu.Item key="custom">添加自定义模型</Menu.Item>
    </Menu>
  );

  return (
    <div>
      <div className="mb-4">
        <Dropdown overlay={addModelMenu}>
          <Button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
            添加模型 <DownOutlined className="ml-1" />
          </Button>
        </Dropdown>
      </div>
      
      <div className="model-list">
        {modelConfigs.map((config) => {
          const isCurrent = currentModelId === `${config.provider}|${config.model}`;
          const canDelete = modelConfigs.length > 1;

          return (
            <div key={`${config.provider}-${config.model}`} className="model-item mb-3 p-3 border rounded">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg">
                    {config.providerName} - {config.modelName}
                    {config.isDefault && <span className="ml-2 text-sm text-green-500">(默认)</span>}
                  </h3>
                  <p className="text-sm text-gray-500">模型ID: {config.model}</p>
                  <p className="text-sm text-gray-500">接口: {config.baseUrl}</p>
                </div>
                <div>
                  <Space>
                    <Button 
                      onClick={() => handleUseModelConfig(config)}
                      disabled={isCurrent}
                      className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 px-4 py-2 h-9 ${
                        isCurrent 
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700'
                      }`}
                    >
                      使用
                    </Button>
                    <Button 
                      onClick={() => handleEditModelConfig(config)}
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                    >
                      编辑
                    </Button>
                    <Button 
                      onClick={() => showDeleteConfirmModal(config.provider, config.model)}
                      disabled={!canDelete}
                      className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 px-4 py-2 h-9 ${
                        !canDelete
                        ? 'border border-gray-300 text-gray-400 cursor-not-allowed'
                        : 'border border-red-500 text-red-500 hover:bg-red-50 active:bg-red-100'
                      }`}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <Modal
        title="删除模型配置"
        open={isConfirmModalVisible}
        onCancel={handleCancelDelete}
        footer={[
          <Button 
            key="cancel"
            onClick={handleCancelDelete}
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 mr-2"
          >
            取消
          </Button>,
          <Button 
            key="confirm"
            onClick={handleConfirmDelete}
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 bg-red-600 text-white hover:bg-red-700 active:bg-red-800 h-9 px-4 py-2"
          >
            确认删除
          </Button>,
        ]}
      >
        <p>确定要删除这个模型配置吗？此操作不可恢复。</p>
      </Modal>
    </div>
  );
}; 