export type FontOption = {
  label: string;
  value: string;
};

export type FontGroup = {
  label: string;
  options: FontOption[];
};

export const FONT_FAMILY_GROUPS: FontGroup[] = [
  {
    label: '主题字体',
    options: [
      { label: '微软雅黑', value: 'Microsoft YaHei' },
      { label: '等线', value: 'DengXian' },
      { label: '等线 Light', value: 'DengXian Light' },
      { label: 'Arial', value: 'Arial' },
      { label: '标题字体', value: 'Title Font' },
      { label: '正文字体', value: 'Text Font' },
    ],
  },
  {
    label: '中文字体',
    options: [
      { label: '微软雅黑', value: 'Microsoft YaHei' },
      { label: '微软雅黑 Light', value: 'Microsoft YaHei Light' },
      { label: '宋体', value: 'SimSun' },
      { label: '黑体', value: 'SimHei' },
      { label: '楷体', value: 'KaiTi' },
      { label: '隶书', value: 'LiSu' },
      { label: '幼圆', value: 'YouYuan' },
      { label: '华文细黑', value: 'STXihei' },
      { label: '华文楷体', value: 'STKaiti' },
      { label: '华文宋体', value: 'STSong' },
      { label: '新宋体', value: 'NSimSun' },
      { label: '仿宋', value: 'FangSong' },
    ],
  },
  {
    label: '西文字体',
    options: [
      { label: 'Arial', value: 'Arial' },
      { label: 'Arial Black', value: 'Arial Black' },
      { label: 'Calibri', value: 'Calibri' },
      { label: 'Calibri Light', value: 'Calibri Light' },
      { label: 'Cambria', value: 'Cambria' },
      { label: 'Candara', value: 'Candara' },
      { label: 'Comic Sans MS', value: 'Comic Sans MS' },
      { label: 'Consolas', value: 'Consolas' },
      { label: 'Constantia', value: 'Constantia' },
      { label: 'Corbel', value: 'Corbel' },
      { label: 'Courier New', value: 'Courier New' },
      { label: 'Georgia', value: 'Georgia' },
      { label: 'Impact', value: 'Impact' },
      { label: 'Lucida Sans Unicode', value: 'Lucida Sans Unicode' },
      { label: 'Palatino Linotype', value: 'Palatino Linotype' },
      { label: 'Tahoma', value: 'Tahoma' },
      { label: 'Times New Roman', value: 'Times New Roman' },
      { label: 'Trebuchet MS', value: 'Trebuchet MS' },
      { label: 'Verdana', value: 'Verdana' },
    ],
  },
];

// 扁平化字体组，便于查找
export const FONT_FAMILIES = FONT_FAMILY_GROUPS.flatMap(group => group.options);

// 默认字体
export const DEFAULT_FONT = FONT_FAMILIES.find(font => font.label === '微软雅黑') || FONT_FAMILIES[0]; 