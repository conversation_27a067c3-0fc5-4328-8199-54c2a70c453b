import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { EditorState } from '@tiptap/pm/state'

// 定义批注相关类型
export type SuggestionItem = {
  id: string;
  text: string;
  createdAt: string;
  accepted?: boolean; // 标记是否已接受该修订建议
}

export type CommentState = 'active' | 'hidden' | 'resolved'

export type Comment = {
  id: string;
  anchor: { from: number; to: number };
  original: string;
  content?: string;  // 批注内容
  suggestions: SuggestionItem[];
  state: CommentState;
  createdAt: string;
  updatedAt: string;
}

// 批注包类型
export type CommentPackage = {
  version: string;
  docHash: string;
  comments: Comment[];
}

// 批注存储的状态和操作
interface CommentStore {
  // 状态
  comments: Comment[];
  activeCommentId: string | null;
  showCommentPanel: boolean;
  
  // 文档相关
  docHash: string;
  
  // 操作
  addComment: (comment: Omit<Comment, 'createdAt' | 'updatedAt'>) => void;
  updateComment: (id: string, data: Partial<Comment>) => void;
  deleteComment: (id: string) => void;
  setActiveComment: (id: string | null) => void;
  toggleCommentPanel: () => void;
  setCommentState: (id: string, state: CommentState) => void;
  addSuggestion: (commentId: string, text: string, accepted?: boolean) => void;
  importComments: (comments: Comment[]) => void;
  
  // 辅助方法
  getCommentById: (id: string) => Comment | undefined;
  getDocPositionComments: (state: EditorState) => { id: string; from: number; to: number }[];
}

// 创建Zustand存储
export const useCommentStore = create<CommentStore>()(
  persist(
    (set, get) => ({
      comments: [],
      activeCommentId: null,
      showCommentPanel: true,
      docHash: '',
      
      // 添加批注
      addComment: (comment: Omit<Comment, 'createdAt' | 'updatedAt'>) => {
        const now = new Date().toISOString();
        const newComment: Comment = {
          ...comment,
          createdAt: now,
          updatedAt: now,
        };
        
        set((state: CommentStore) => ({
          comments: [...state.comments, newComment],
          activeCommentId: newComment.id,
        }));
      },
      
      // 更新批注
      updateComment: (id: string, data: Partial<Comment>) => {
        set((state: CommentStore) => ({
          comments: state.comments.map((comment: Comment) =>
            comment.id === id
              ? { ...comment, ...data, updatedAt: new Date().toISOString() }
              : comment
          ),
        }));
      },
      
      // 删除批注
      deleteComment: (id: string) => {
        set((state: CommentStore) => ({
          comments: state.comments.filter((comment: Comment) => comment.id !== id),
          activeCommentId: state.activeCommentId === id ? null : state.activeCommentId,
        }));
      },
      
      // 设置当前激活的批注
      setActiveComment: (id: string | null) => {
        set({ activeCommentId: id });
      },
      
      // 切换批注面板显示状态 (保留接口兼容，但不再实际切换)
      toggleCommentPanel: () => {
        // 不再执行任何操作，保持批注面板始终可见
      },
      
      // 设置批注状态
      setCommentState: (id: string, state: CommentState) => {
        set((prevState: CommentStore) => ({
          comments: prevState.comments.map((comment: Comment) =>
            comment.id === id
              ? { ...comment, state, updatedAt: new Date().toISOString() }
              : comment
          ),
        }));
      },
      
      // 添加修订建议
      addSuggestion: (commentId: string, text: string, accepted?: boolean) => {
        const suggestionId = Math.random().toString(36).substring(2, 10);
        set((state: CommentStore) => ({
          comments: state.comments.map((comment: Comment) =>
            comment.id === commentId
              ? {
                  ...comment,
                  suggestions: [
                    ...comment.suggestions,
                    { id: suggestionId, text, createdAt: new Date().toISOString(), accepted },
                  ],
                  updatedAt: new Date().toISOString(),
                }
              : comment
          ),
        }));
      },
      
      // 导入批注
      importComments: (comments: Comment[]) => {
        set({ comments });
      },
      
      // 通过ID获取批注
      getCommentById: (id: string) => {
        return get().comments.find((comment: Comment) => comment.id === id);
      },
      
      // 获取文档中所有批注的位置
      getDocPositionComments: (state: EditorState) => {
        return get().comments.map((comment: Comment) => ({
          id: comment.id,
          from: comment.anchor.from,
          to: comment.anchor.to,
        }));
      },
    }),
    {
      name: 'tiptap-comment-storage',
    }
  )
) 