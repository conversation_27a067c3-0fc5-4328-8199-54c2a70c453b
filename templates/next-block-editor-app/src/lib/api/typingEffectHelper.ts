import { Editor } from '@tiptap/react';

/**
 * 在编辑器中实现打字效果
 * @param editor 编辑器实例
 * @param text 要添加的文本
 * @param speed 打字速度(ms)
 * @param charsPerFrame 每次添加的字符数
 * @param onComplete 打字效果完成后的回调函数
 */
export function applyTypingEffect(
  editor: Editor, 
  text: string, 
  speed = 50, 
  charsPerFrame = 1,
  onComplete?: () => void
): void {
  if (!editor || !text) return;
  
  let position = 0;
  
  const typeNextChunk = () => {
    if (position >= text.length) {
      // 打字效果完成，执行回调
      if (onComplete) {
        onComplete();
      }
      return;
    }
    
    const charsToAdd = Math.min(charsPerFrame, text.length - position);
    const chunk = text.substring(position, position + charsToAdd);
    
    // 安全地插入文本
    try {
      editor.commands.insertContent(chunk);
      position += charsToAdd;
      
      // 安排下一次插入
      setTimeout(typeNextChunk, speed);
    } catch (error) {
      console.error('打字效果执行错误:', error);
      // 发生错误时也尝试执行回调
      if (onComplete) {
        onComplete();
      }
    }
  };
  
  // 开始打字效果
  typeNextChunk();
} 