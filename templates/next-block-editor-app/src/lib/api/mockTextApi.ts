// 模拟API响应延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 示例响应数据
const sampleResponses: Record<string, string[]> = {
  default: [
    "这是一段生成的补充文本，展示了打字效果的实现。",
    "人工智能正在改变我们的生活方式，包括工作、学习和娱乐。",
    "编辑器是内容创作的核心工具，良好的用户体验至关重要。"
  ],
  greeting: [
    "您好！很高兴为您提供帮助。",
    "欢迎使用我们的编辑器，希望您喜欢这个打字效果功能。"
  ],
  technical: [
    "Tiptap是一个基于ProseMirror的现代富文本编辑器框架。",
    "React和Next.js的组合为前端开发提供了强大的工具。"
  ]
};

/**
 * 模拟获取文本数据的API
 * @param selectedText 选中的文本
 * @returns 返回与选中文本相关的补充文本
 */
export const fetchTextData = async (selectedText: string): Promise<string> => {
  // 模拟网络延迟
  await delay(500);
  
  // 根据选中文本的内容返回不同的响应
  if (selectedText.toLowerCase().includes('你好') || selectedText.toLowerCase().includes('hello')) {
    const responses = sampleResponses.greeting;
    return responses[Math.floor(Math.random() * responses.length)];
  } else if (selectedText.toLowerCase().includes('技术') || selectedText.toLowerCase().includes('编程')) {
    const responses = sampleResponses.technical;
    return responses[Math.floor(Math.random() * responses.length)];
  } else {
    // 默认响应
    const responses = sampleResponses.default;
    return responses[Math.floor(Math.random() * responses.length)];
  }
}; 