import { ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { Editor } from '@tiptap/react'

/**
 * 合并多个类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 生成唯一ID
 */
export function generateUniqueId(): string {
  return Math.random().toString(36).substring(2, 10) + Date.now().toString(36);
}

/**
 * 检查当前选择的是否为自定义节点（非纯文本）
 */
export function isCustomNodeSelected(editor: Editor, node?: HTMLElement): boolean {
  if (!node) return false

  // 检查是否为图片、表格、代码块等特殊节点
  const isImage = node.classList && 
    node.classList.contains('ProseMirror-selectednode') && 
    (node.nodeName === 'IMG' || node.querySelector && node.querySelector('img') !== null)
  
  const isTable = node.nodeName === 'TABLE' || 
    (node.closest && node.closest('table') !== null)
  
  const isCodeBlock = editor.isActive('codeBlock')
  
  const isHorizontalRule = editor.isActive('horizontalRule')

  return isImage || isTable || isCodeBlock || isHorizontalRule
}

/**
 * 检查是否选中了文本
 */
export function isTextSelected({ editor }: { editor: Editor }): boolean {
  if (!editor || !editor.state) {
    return false
  }
  
  const { from, to, empty } = editor.state.selection
  
  // 如果选区为空或选区范围为0，表示未选择文本
  if (empty || from === to) {
    return false
  }
  
  // 检查是否选中的是实际文本内容，而不是特殊节点
  const selectedText = editor.state.doc.textBetween(from, to, ' ')
  if (!selectedText.trim()) {
    return false
  }
  
  return true
}

/**
 * 从数组中随机选择一个元素
 */
export function randomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

/**
 * 获取渲染容器
 * 用于获取菜单或弹出框的渲染容器
 */
export function getRenderContainer(element?: HTMLElement | null): HTMLElement {
  if (element && element instanceof HTMLElement) {
    return element
  }
  
  return document.body
} 