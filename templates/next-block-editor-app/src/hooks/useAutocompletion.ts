import { useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { useXAgentService } from '@/components/AI/services/XAgentService'

export const useAutocompletion = () => {
  const xAgentService = useXAgentService()

  // 返回一个获取suggestion的方法，直接用大模型
  const getSuggestion = useCallback(async (editor: Editor | null) => {
    if (!editor) return ''
    const text = editor.state.doc.textBetween(
      editor.state.selection.from - 30 > 0 ? editor.state.selection.from - 30 : 0,
      editor.state.selection.from
    )
    if (!text.trim()) return ''
    // 这里可以自定义prompt模板
    const prompt = `请基于上下文补全一句中文，尽量自然：${text}`
    const { response } = await xAgentService.sendMessage(prompt)
    const clean = (response || '').trim()
    console.log('response', clean)
    return clean
  }, [xAgentService])
  return { getSuggestion }
} 