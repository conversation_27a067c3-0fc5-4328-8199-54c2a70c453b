import React from 'react';
import AIChatPanel from '../components/AI/AIChatPanel';
import { ConfigProvider } from 'antd';
import { useRouter } from 'next/router';

/**
 * AI Copilot 页面
 * 展示AI助手对话界面
 */
const AICopilotPage: React.FC = () => {
  const router = useRouter();

  // 处理AI面板关闭
  const handleAIChatClose = () => {
    // 可以选择返回上一页或导航到其他页面
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 md:p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">AI Copilot</h1>
        <div className="h-[calc(100vh-180px)] min-h-[500px] bg-white rounded-lg shadow">
          <ConfigProvider
            theme={{
              token: {
                colorPrimary: '#1677ff',
                borderRadius: 8,
              },
            }}
          >
            <AIChatPanel onClose={handleAIChatClose} />
          </ConfigProvider>
        </div>
      </div>
    </div>
  );
};

export default AICopilotPage; 