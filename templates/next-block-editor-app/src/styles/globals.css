/* 缩进类 */
.indent-1 {
  margin-left: 20px !important;
  border-left: 4px solid #e5e7eb !important;
  padding-left: 10px !important;
}

.indent-2 {
  margin-left: 40px !important;
  border-left: 4px solid #d1d5db !important;
  padding-left: 10px !important;
}

.indent-3 {
  margin-left: 60px !important;
  border-left: 4px solid #9ca3af !important;
  padding-left: 10px !important;
}

.indent-4 {
  margin-left: 80px !important;
  border-left: 4px solid #6b7280 !important;
  padding-left: 10px !important;
}

.indent-5 {
  margin-left: 100px !important;
  border-left: 4px solid #4b5563 !important;
  padding-left: 10px !important;
}

/* 基于data-indent的缩进样式 */
[data-indent="1"] {
  margin-left: 20px !important;
  border-left: 4px solid #e5e7eb !important;
  padding-left: 10px !important;
}

[data-indent="2"] {
  margin-left: 40px !important;
  border-left: 4px solid #d1d5db !important;
  padding-left: 10px !important;
}

[data-indent="3"] {
  margin-left: 60px !important;
  border-left: 4px solid #9ca3af !important;
  padding-left: 10px !important;
}

[data-indent="4"] {
  margin-left: 80px !important;
  border-left: 4px solid #6b7280 !important;
  padding-left: 10px !important;
}

[data-indent="5"] {
  margin-left: 100px !important;
  border-left: 4px solid #4b5563 !important;
  padding-left: 10px !important;
}

/* 添加模型选择下拉框的样式 */
.model-select-dropdown .ant-select-item {
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 0;
}

.model-select-dropdown .ant-select-item:hover {
  background-color: rgba(0, 144, 255, 0.1);
}

.model-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(0, 144, 255, 0.2);
}

.ant-select-selection-item {
  display: flex;
  align-items: center;
}

/* Markdown暗色主题样式 */
.markdown-content {
  line-height: 1.6;
  color: #c9d1d9;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #e1e4e8;
}

.markdown-content p {
  margin-bottom: 1em;
  color: #c9d1d9;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
  color: #c9d1d9;
}

.markdown-content blockquote {
  border-left: 4px solid #30363d;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  color: #8b949e;
}

/* 代码块样式 */
.markdown-content pre {
  margin: 0.5em 0;
  border-radius: 6px;
  background-color: #0d1117;
  overflow-x: auto;
  max-width: 100%;
  position: relative;
  border: 1px solid #30363d;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.markdown-content pre code {
  padding: 0.75em 1em;
  white-space: pre;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
  line-height: 1.5;
  display: block;
  color: #e1e4e8;
}

.markdown-content code {
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
  background-color: rgba(110, 118, 129, 0.4);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  color: #e1e4e8;
}

.markdown-content a {
  color: #58a6ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #30363d;
  padding: 6px 13px;
}

.markdown-content table th {
  background-color: #161b22;
  font-weight: 600;
  color: #e1e4e8;
}

/* highlight.js GitHub深色主题风格 */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
  color: #e1e4e8;
  background: #0d1117;
  border-radius: 6px;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
}

/* 针对Java关键字和标识符的高亮 - 深色主题 */
.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #ff7b72;
  font-weight: bold;
}

.hljs-built_in,
.hljs-type {
  color: #ff7b72;
}

.hljs-class .hljs-title {
  color: #d2a8ff;
  font-weight: bold;
}

.hljs-title.class_,
.hljs-title.function_ {
  color: #d2a8ff;
}

.hljs-function .hljs-keyword {
  color: #ff7b72;
}

.hljs-function .hljs-title {
  color: #d2a8ff;
}

.hljs-literal,
.hljs-number {
  color: #79c0ff;
  font-weight: normal;
}

.hljs-comment {
  color: #8b949e;
  font-style: italic;
}

.hljs-doctag,
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-tag {
  color: #7ee787;
}

.hljs-attr,
.hljs-name,
.hljs-section,
.hljs-selector-class,
.hljs-selector-id,
.hljs-selector-pseudo {
  color: #79c0ff;
}

.hljs-addition {
  color: #7ee787;
  background-color: #033a16;
}

.hljs-deletion {
  color: #ff7b72;
  background-color: #67060c;
}

.hljs-string,
.hljs-meta .hljs-string,
.hljs-regexp {
  color: #a5d6ff;
}

/* Java特定语法 - 深色主题 */
.language-java .hljs-keyword {
  color: #ff7b72;
  font-weight: bold;
}

.language-java .hljs-class .hljs-title,
.language-java .hljs-title.class_ {
  color: #d2a8ff;
  font-weight: bold;
}

.language-java .hljs-function .hljs-title,
.language-java .hljs-title.function_ {
  color: #d2a8ff;
}

.language-java .hljs-string {
  color: #a5d6ff;
}

.language-java .hljs-comment {
  color: #8b949e;
  font-style: italic;
}

.language-java .hljs-number,
.language-java .hljs-literal {
  color: #79c0ff;
}

/* 代码块复制按钮样式 */
.code-copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.3rem 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c9d1d9;
  font-size: 14px;
  z-index: 10;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
}

.markdown-content pre:hover .code-copy-button {
  opacity: 1;
}

.code-copy-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

.code-copy-button:active {
  background-color: rgba(255, 255, 255, 0.25);
}

.code-copy-button.copied {
  color: #7ee787;
  background-color: rgba(56, 139, 95, 0.4);
  border-color: rgba(56, 139, 95, 0.6);
} 