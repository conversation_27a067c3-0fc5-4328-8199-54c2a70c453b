.ProseMirror {
  @apply caret-black dark:caret-white outline-0 pr-8 pl-20 py-16 z-0 lg:pl-8 lg:pr-8 mx-auto max-w-2xl;

  .selection {
    @apply inline;
  }

  .selection,
  *::selection {
    @apply bg-black/10 dark:bg-white/20 inline;
  }

  & > .react-renderer {
    @apply my-12 first:mt-0 last:mb-0;
  }

  &.resize-cursor {
    @apply cursor-col-resize;
  }

  .ProseMirror-gapcursor {
    @apply relative w-full max-w-2xl mx-auto;

    &:after {
      @apply border-t-black/40 dark:border-t-white/40 w-full -top-[1.5em] max-w-2xl mx-auto left-0 right-0;
    }
  }
}

[data-theme='slash-command'] {
  width: 1000vw;
}

@import './partials/animations.css';
@import './partials/blocks.css';
@import './partials/code.css';
@import './partials/collab.css';
@import './partials/lists.css';
@import './partials/placeholder.css';
@import './partials/table.css';
@import './partials/typography.css';

.ProseMirror .has-ghost-text::after {
  content: attr(data-ghost);
  color: #bdbdbd;
  opacity: 0.8;
  pointer-events: none;
  font-style: italic;
  margin-left: 2px;
  animation: ghost-typing 1.2s steps(20, end) 1;
  white-space: pre;
}

@keyframes ghost-typing {
  from {
    width: 0;
    opacity: 0.5;
  }
  to {
    width: 100%;
    opacity: 0.8;
  }
}
