{"name": "tiptap-demos-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write --ignore-path .gitignore \"./**/*.{js,jsx,ts,tsx,json,css}\""}, "dependencies": {"@ant-design/x": "^1.1.0", "@fontsource/inter": "^5.1.1", "@hocuspocus/provider": "^2.15.2", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-slot": "^1.1.1", "@svgr/webpack": "^8.1.0", "@tippyjs/react": "^4.2.6", "@tiptap-pro/extension-details": "^2.17.3", "@tiptap-pro/extension-details-content": "^2.17.3", "@tiptap-pro/extension-details-summary": "^2.17.3", "@tiptap-pro/extension-drag-handle": "^2.17.3", "@tiptap-pro/extension-drag-handle-react": "^2.17.3", "@tiptap-pro/extension-emoji": "^2.17.3", "@tiptap-pro/extension-file-handler": "^2.17.3", "@tiptap-pro/extension-mathematics": "^2.17.3", "@tiptap-pro/extension-node-range": "^2.17.3", "@tiptap-pro/extension-table-of-contents": "^2.17.3", "@tiptap-pro/extension-unique-id": "^2.17.3", "@tiptap/core": "^2.11.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-code-block": "^2.11.5", "@tiptap/extension-code-block-lowlight": "^2.11.5", "@tiptap/extension-collaboration": "^2.11.5", "@tiptap/extension-collaboration-cursor": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-document": "^2.11.5", "@tiptap/extension-dropcursor": "^2.11.5", "@tiptap/extension-focus": "^2.11.5", "@tiptap/extension-font-family": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/suggestion": "^2.11.5", "@types/diff-match-patch": "^1.0.36", "@types/markdown-it": "^14.1.2", "autoprefixer": "10.4.20", "cal-sans": "^1.0.1", "clsx": "^2.1.1", "diff-match-patch": "^1.0.5", "iframe-resizer": "^5.3.2", "jsonwebtoken": "^9.0.2", "lowlight": "^3.3.0", "lucide-react": "^0.474.0", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "nanoid": "^5.0.9", "next": "^14.2.23", "postcss": "8.5.1", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.1", "tailwind-merge": "^3.2.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "uuid": "^11.0.5", "y-prosemirror": "^1.2.15", "yjs": "^13.6.23", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.8", "@types/lodash.debounce": "^4.0.9", "@types/node": "22.13.1", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "eslint": "9.19.0", "eslint-config-next": "^15.1.6", "eslint-plugin-prettier": "^5.2.3", "typescript": "5.7.3"}}