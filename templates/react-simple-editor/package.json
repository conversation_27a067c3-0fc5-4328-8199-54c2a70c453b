{"name": "react-simple-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "react": "^18.3.1", "react-dom": "^18.3.1", "remixicon": "^4.6.0"}, "devDependencies": {"@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.3.3", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.14", "sass": "^1.81.0", "typescript": "^5.7.2", "vite": "^5.4.11"}}