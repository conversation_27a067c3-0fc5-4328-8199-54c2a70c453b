{"name": "@tiptap-pro/templates", "private": true, "version": "0.0.0", "workspaces": ["templates/*"], "description": "Templates for an advanced block editor using Tiptap", "scripts": {"dev": "npm run dev --prefix ./templates/next-block-editor-app", "dev:simple-editor": "npm run dev --prefix ./templates/react-simple-editor", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": {"name": "Tiptap GmbH", "url": "https://tiptap.dev"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@radix-ui/react-dialog": "^1.1.7", "@types/dompurify": "^3.0.5", "dompurify": "^3.2.5", "highlight.js": "^11.11.1", "markdown-it-highlightjs": "^4.2.0", "openai": "^4.93.0", "tdesign-react": "^1.11.4"}}