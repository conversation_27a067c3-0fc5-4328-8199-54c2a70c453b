{"typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "files.exclude": {"**/.cache": true, "**/public": false}, "files.associations": {"*.js": "javascriptreact"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.formatOnSave": true}, "[javascriptreact]": {"editor.formatOnSave": true}, "[typescript]": {"editor.formatOnSave": true}, "[typescriptreact]": {"editor.formatOnSave": true}, "[markdown]": {"editor.formatOnSave": true}, "[mdx]": {"editor.formatOnSave": true}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}