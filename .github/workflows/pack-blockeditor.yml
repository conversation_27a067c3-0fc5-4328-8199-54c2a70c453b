name: Pack blockeditor template

on:
  release:
    types: [created]

jobs:
  package_pro:
    name: Pack pro version of blockeditor
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Create ZIP archive
        run: |
          rm -rf ./templates/next-block-editor-app/src/extensions/Ai/AiFree.ts
          rm -rf ./templates/next-block-editor-app/src/extensions/AiImage/AiImageFree.ts
          rm -rf ./templates/next-block-editor-app/src/extensions/AiWriter/AiWriterFree.ts
          cd ./templates/next-block-editor-app && zip -r ../../blockeditor-template-pro.zip ./
      - name: Create Pro release on GitHub
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          fail_on_unmatched_files: true
          files: blockeditor-template-pro.zip
  package_basic:
    name: Pack basic version of blockeditor
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install jq
        run: sudo apt update && sudo apt install jq
      - name: Create ZIP archive
        run: |
          bash ./convert-to-free-version.sh
          rm -rf ./templates/next-block-editor-app/src/extensions/Ai/AiFree.ts
          rm -rf ./templates/next-block-editor-app/src/extensions/AiImage/AiImageFree.ts
          rm -rf ./templates/next-block-editor-app/src/extensions/AiWriter/AiWriterFree.ts
          cd ./templates/next-block-editor-app && zip -r ../../blockeditor-template-basic.zip ./
      - name: Create Basic release on GitHub
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          fail_on_unmatched_files: true
          files: blockeditor-template-basic.zip
